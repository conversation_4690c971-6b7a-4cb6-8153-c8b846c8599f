"""
PandasAI Chat Interface Component
"""
import streamlit as st
import pandas as pd
from typing import Dict, List, Any, Optional
import time
import os
from PIL import Image
import plotly.graph_objects as go
import plotly.express as px

from ..utils.pandasai_client import PandasAIClient
from ..components.conversation_manager import ConversationManager
from ..config.pandasai_config import PandasAIConfig

class PandasAIChatInterface:
    """Enhanced chat interface for PandasAI with rich features"""
    
    def __init__(self, container_key: str = "pandasai_chat"):
        self.container_key = container_key
        self.conversation_manager = ConversationManager(f"{container_key}_conversations")
        self.config = PandasAIConfig.get_config()
        self.client = None
        
    def initialize_client(self, api_key: str, dataframe: pd.DataFrame = None) -> bool:
        """Initialize PandasAI client"""
        try:
            self.client = PandasAIClient(api_key, self.config["pandasai"])
            
            if dataframe is not None:
                success = self.client.initialize_agent(dataframe)
                if success:
                    # Update dataset info in conversation
                    dataset_info = {
                        "shape": dataframe.shape,
                        "columns": dataframe.columns.tolist(),
                        "dtypes": dataframe.dtypes.to_dict(),
                        "memory_usage": dataframe.memory_usage(deep=True).sum(),
                        "null_counts": dataframe.isnull().sum().to_dict()
                    }
                    self.conversation_manager.update_dataset_info(dataset_info)
                return success
            return True
        except Exception as e:
            st.error(f"Failed to initialize PandasAI client: {str(e)}")
            return False
    
    def render_chat_interface(self, height: int = 600, show_suggestions: bool = True):
        """Render the main chat interface"""
        
        # Chat container
        chat_container = st.container()
        
        with chat_container:
            # Chat header
            col1, col2, col3 = st.columns([3, 1, 1])
            
            with col1:
                st.subheader("🤖 PandasAI Assistant")
            
            with col2:
                if st.button("🗑️ Clear Chat", key=f"{self.container_key}_clear"):
                    self._clear_conversation()
                    st.rerun()
            
            with col3:
                if st.button("📥 Export", key=f"{self.container_key}_export"):
                    self._export_conversation()
            
            # Chat messages container
            messages_container = st.container()
            
            with messages_container:
                self._render_messages(height - 200)
            
            # Input area
            self._render_input_area()
            
            # Suggestions
            if show_suggestions:
                self._render_suggestions()
    
    def _render_messages(self, height: int):
        """Render chat messages"""
        messages = self.conversation_manager.get_messages()
        
        if not messages:
            st.info("👋 Welcome! Upload a dataset and start asking questions about your data.")
            return
        
        # Create scrollable container for messages
        with st.container():
            for message in messages:
                self._render_message(message)
    
    def _render_message(self, message: Dict[str, Any]):
        """Render a single message"""
        message_type = message["type"]
        content = message["content"]
        metadata = message.get("metadata", {})
        
        if message_type == "user":
            with st.chat_message("user"):
                st.write(content)
        
        elif message_type == "assistant":
            with st.chat_message("assistant"):
                st.write(content)
                
                # Show generated code if available
                if metadata.get("code"):
                    with st.expander("📝 Generated Code"):
                        st.code(metadata["code"], language="python")
                
                # Show chart if available
                if metadata.get("chart"):
                    self._render_chart(metadata["chart"])
        
        elif message_type == "error":
            with st.chat_message("assistant"):
                st.error(f"❌ {content}")
        
        elif message_type == "system":
            with st.chat_message("assistant"):
                st.info(f"ℹ️ {content}")
    
    def _render_chart(self, chart_path: str):
        """Render chart from file path"""
        try:
            if chart_path.endswith('.png') or chart_path.endswith('.jpg'):
                if os.path.exists(chart_path):
                    image = Image.open(chart_path)
                    st.image(image, caption="Generated Chart")
            elif chart_path.endswith('.html'):
                if os.path.exists(chart_path):
                    with open(chart_path, 'r') as f:
                        html_content = f.read()
                    st.components.v1.html(html_content, height=400)
        except Exception as e:
            st.warning(f"Could not display chart: {str(e)}")
    
    def _render_input_area(self):
        """Render chat input area"""
        
        # Check if client is initialized
        if self.client is None:
            st.warning("⚠️ Please configure API key and upload dataset to start chatting")
            return
        
        # Chat input
        user_input = st.chat_input("Ask me anything about your data...", key=f"{self.container_key}_input")
        
        if user_input:
            self._process_user_input(user_input)
    
    def _process_user_input(self, user_input: str):
        """Process user input and get AI response"""
        
        # Add user message to conversation
        self.conversation_manager.add_message("user", user_input)
        
        # Show user message immediately
        with st.chat_message("user"):
            st.write(user_input)
        
        # Show thinking indicator
        with st.chat_message("assistant"):
            with st.spinner("🤔 Thinking..."):
                # Get response from PandasAI
                response, code, chart = self.client.chat(user_input)
            
            # Display response
            st.write(response)
            
            # Prepare metadata
            metadata = {}
            if code:
                metadata["code"] = code
                with st.expander("📝 Generated Code"):
                    st.code(code, language="python")
            
            if chart:
                metadata["chart"] = chart
                self._render_chart(chart)
            
            # Add assistant message to conversation
            self.conversation_manager.add_message("assistant", response, metadata)
        
        # Rerun to update the interface
        st.rerun()
    
    def _render_suggestions(self):
        """Render suggested questions"""
        if self.client is None:
            return
        
        suggestions = self.client.get_suggested_questions()
        
        if suggestions:
            st.subheader("💡 Suggested Questions")
            
            # Create columns for suggestions
            cols = st.columns(2)
            
            for i, suggestion in enumerate(suggestions[:6]):  # Show max 6 suggestions
                col = cols[i % 2]
                
                with col:
                    if st.button(
                        suggestion, 
                        key=f"{self.container_key}_suggestion_{i}",
                        help="Click to ask this question"
                    ):
                        self._process_user_input(suggestion)
    
    def _clear_conversation(self):
        """Clear current conversation"""
        current_id = self.conversation_manager.get_current_conversation_id()
        if current_id:
            self.conversation_manager.delete_conversation(current_id)
        
        if self.client:
            self.client.clear_conversation()
        
        st.success("Conversation cleared!")
    
    def _export_conversation(self):
        """Export conversation"""
        conversation_data = self.conversation_manager.export_conversation()
        
        if conversation_data != "{}":
            st.download_button(
                label="📥 Download Conversation",
                data=conversation_data,
                file_name=f"pandasai_conversation_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.json",
                mime="application/json",
                key=f"{self.container_key}_download"
            )
        else:
            st.warning("No conversation to export")
    
    def get_conversation_summary(self) -> Dict[str, Any]:
        """Get summary of current conversation"""
        return self.conversation_manager.get_conversation_summary()
    
    def render_compact_chat(self, height: int = 400):
        """Render a compact version of the chat interface"""
        
        # Compact header
        st.markdown("**🤖 Quick Chat**")
        
        # Messages in smaller container
        with st.container():
            messages = self.conversation_manager.get_messages()
            
            # Show only last few messages
            recent_messages = messages[-5:] if len(messages) > 5 else messages
            
            for message in recent_messages:
                if message["type"] == "user":
                    st.markdown(f"**You:** {message['content']}")
                elif message["type"] == "assistant":
                    st.markdown(f"**AI:** {message['content']}")
        
        # Compact input
        user_input = st.text_input("Quick question:", key=f"{self.container_key}_compact_input")
        
        if st.button("Send", key=f"{self.container_key}_compact_send") and user_input:
            self._process_user_input(user_input)
