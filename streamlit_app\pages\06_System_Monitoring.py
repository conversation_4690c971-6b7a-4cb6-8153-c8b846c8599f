"""
System Monitoring & Performance Analytics for PandasAI Operations
"""
import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import psutil
import time
from datetime import datetime, timedelta
import json
import os
import sys

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from components.pandasai_chat import PandasAIChatInterface
from components.ai_integration import AIIntegrationManager
from config.pandasai_config import PandasAIConfig

# Page configuration
st.set_page_config(
    page_title="System Monitoring - AI Data Explorer",
    page_icon="📊",
    layout="wide"
)

def initialize_session_state():
    """Initialize session state for System Monitoring page"""
    if 'api_key' not in st.session_state:
        st.session_state.api_key = ""
    
    if 'current_dataframe' not in st.session_state:
        st.session_state.current_dataframe = None
    
    if 'performance_metrics' not in st.session_state:
        st.session_state.performance_metrics = []
    
    if 'chat_analytics' not in st.session_state:
        st.session_state.chat_analytics = {
            "total_queries": 0,
            "successful_queries": 0,
            "failed_queries": 0,
            "average_response_time": 0,
            "query_history": []
        }
    
    if 'system_metrics' not in st.session_state:
        st.session_state.system_metrics = []
    
    if 'monitoring_active' not in st.session_state:
        st.session_state.monitoring_active = False

def get_system_metrics():
    """Get current system performance metrics"""
    
    try:
        # CPU usage
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # Memory usage
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        memory_used_gb = memory.used / (1024**3)
        memory_total_gb = memory.total / (1024**3)
        
        # Disk usage
        disk = psutil.disk_usage('/')
        disk_percent = disk.percent
        disk_used_gb = disk.used / (1024**3)
        disk_total_gb = disk.total / (1024**3)
        
        # Network I/O (if available)
        try:
            network = psutil.net_io_counters()
            bytes_sent = network.bytes_sent / (1024**2)  # MB
            bytes_recv = network.bytes_recv / (1024**2)  # MB
        except:
            bytes_sent = bytes_recv = 0
        
        metrics = {
            "timestamp": datetime.now(),
            "cpu_percent": cpu_percent,
            "memory_percent": memory_percent,
            "memory_used_gb": memory_used_gb,
            "memory_total_gb": memory_total_gb,
            "disk_percent": disk_percent,
            "disk_used_gb": disk_used_gb,
            "disk_total_gb": disk_total_gb,
            "network_sent_mb": bytes_sent,
            "network_recv_mb": bytes_recv
        }
        
        return metrics
        
    except Exception as e:
        st.error(f"Error getting system metrics: {str(e)}")
        return None

def track_pandasai_performance(query, response_time, success=True, error_message=None):
    """Track PandasAI query performance"""
    
    query_record = {
        "timestamp": datetime.now(),
        "query": query,
        "response_time": response_time,
        "success": success,
        "error_message": error_message
    }
    
    # Update analytics
    st.session_state.chat_analytics["total_queries"] += 1
    
    if success:
        st.session_state.chat_analytics["successful_queries"] += 1
    else:
        st.session_state.chat_analytics["failed_queries"] += 1
    
    # Update average response time
    total_time = (st.session_state.chat_analytics["average_response_time"] * 
                 (st.session_state.chat_analytics["total_queries"] - 1) + response_time)
    st.session_state.chat_analytics["average_response_time"] = total_time / st.session_state.chat_analytics["total_queries"]
    
    # Add to history
    st.session_state.chat_analytics["query_history"].append(query_record)
    
    # Keep only last 100 queries
    if len(st.session_state.chat_analytics["query_history"]) > 100:
        st.session_state.chat_analytics["query_history"] = st.session_state.chat_analytics["query_history"][-100:]

def render_real_time_metrics():
    """Render real-time system metrics"""
    
    st.markdown("## 📊 Real-Time System Metrics")
    
    # Get current metrics
    current_metrics = get_system_metrics()
    
    if current_metrics:
        # Display current metrics
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("CPU Usage", f"{current_metrics['cpu_percent']:.1f}%")
        
        with col2:
            st.metric("Memory Usage", f"{current_metrics['memory_percent']:.1f}%",
                     f"{current_metrics['memory_used_gb']:.1f}/{current_metrics['memory_total_gb']:.1f} GB")
        
        with col3:
            st.metric("Disk Usage", f"{current_metrics['disk_percent']:.1f}%",
                     f"{current_metrics['disk_used_gb']:.1f}/{current_metrics['disk_total_gb']:.1f} GB")
        
        with col4:
            st.metric("Network I/O", f"↑{current_metrics['network_sent_mb']:.1f} MB",
                     f"↓{current_metrics['network_recv_mb']:.1f} MB")
        
        # Add to metrics history
        st.session_state.system_metrics.append(current_metrics)
        
        # Keep only last 50 measurements
        if len(st.session_state.system_metrics) > 50:
            st.session_state.system_metrics = st.session_state.system_metrics[-50:]
        
        # Auto-refresh toggle
        col1, col2 = st.columns(2)
        
        with col1:
            auto_refresh = st.checkbox("Auto-refresh (5s)", value=st.session_state.monitoring_active)
            st.session_state.monitoring_active = auto_refresh
        
        with col2:
            if st.button("🔄 Refresh Now"):
                st.rerun()
        
        # Auto-refresh
        if auto_refresh:
            time.sleep(5)
            st.rerun()

def render_performance_charts():
    """Render performance monitoring charts"""
    
    st.markdown("## 📈 Performance Trends")
    
    if len(st.session_state.system_metrics) < 2:
        st.info("📊 Collecting metrics... Charts will appear after a few measurements")
        return
    
    # Create DataFrame from metrics
    metrics_df = pd.DataFrame(st.session_state.system_metrics)
    
    # Create subplots
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=("CPU Usage Over Time", "Memory Usage Over Time", 
                       "Disk Usage", "Network Activity"),
        specs=[[{"secondary_y": False}, {"secondary_y": False}],
               [{"secondary_y": False}, {"secondary_y": False}]]
    )
    
    # CPU usage chart
    fig.add_trace(
        go.Scatter(x=metrics_df['timestamp'], y=metrics_df['cpu_percent'],
                  mode='lines+markers', name='CPU %', line=dict(color='red')),
        row=1, col=1
    )
    
    # Memory usage chart
    fig.add_trace(
        go.Scatter(x=metrics_df['timestamp'], y=metrics_df['memory_percent'],
                  mode='lines+markers', name='Memory %', line=dict(color='blue')),
        row=1, col=2
    )
    
    # Disk usage (current value as bar)
    current_disk = metrics_df['disk_percent'].iloc[-1]
    fig.add_trace(
        go.Bar(x=['Disk Usage'], y=[current_disk], name='Disk %', 
               marker_color='green'),
        row=2, col=1
    )
    
    # Network activity
    fig.add_trace(
        go.Scatter(x=metrics_df['timestamp'], y=metrics_df['network_sent_mb'],
                  mode='lines', name='Sent MB', line=dict(color='orange')),
        row=2, col=2
    )
    fig.add_trace(
        go.Scatter(x=metrics_df['timestamp'], y=metrics_df['network_recv_mb'],
                  mode='lines', name='Received MB', line=dict(color='purple')),
        row=2, col=2
    )
    
    # Update layout
    fig.update_layout(
        height=600,
        title_text="System Performance Dashboard",
        showlegend=False
    )
    
    # Update y-axes
    fig.update_yaxes(title_text="Percentage (%)", row=1, col=1)
    fig.update_yaxes(title_text="Percentage (%)", row=1, col=2)
    fig.update_yaxes(title_text="Percentage (%)", row=2, col=1)
    fig.update_yaxes(title_text="MB", row=2, col=2)
    
    st.plotly_chart(fig, use_container_width=True)

def render_pandasai_analytics():
    """Render PandasAI performance analytics"""
    
    st.markdown("## 🤖 PandasAI Performance Analytics")
    
    analytics = st.session_state.chat_analytics
    
    # Performance metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total Queries", analytics["total_queries"])
    
    with col2:
        st.metric("Successful Queries", analytics["successful_queries"])
    
    with col3:
        success_rate = (analytics["successful_queries"] / max(analytics["total_queries"], 1)) * 100
        st.metric("Success Rate", f"{success_rate:.1f}%")
    
    with col4:
        st.metric("Avg Response Time", f"{analytics['average_response_time']:.2f}s")
    
    # Query history analysis
    if analytics["query_history"]:
        st.markdown("### Query Performance Over Time")
        
        # Create DataFrame from query history
        query_df = pd.DataFrame(analytics["query_history"])
        
        # Response time chart
        fig = px.line(query_df, x='timestamp', y='response_time',
                     title='Query Response Times',
                     labels={'response_time': 'Response Time (seconds)'})
        
        # Color by success/failure
        fig.update_traces(
            line=dict(color=query_df['success'].map({True: 'green', False: 'red'}))
        )
        
        st.plotly_chart(fig, use_container_width=True)
        
        # Success/failure distribution
        success_counts = query_df['success'].value_counts()
        
        if len(success_counts) > 1:
            fig_pie = px.pie(
                values=success_counts.values,
                names=['Success' if x else 'Failed' for x in success_counts.index],
                title='Query Success Distribution'
            )
            st.plotly_chart(fig_pie, use_container_width=True)
        
        # Recent queries table
        st.markdown("### Recent Queries")
        
        recent_queries = query_df.tail(10).copy()
        recent_queries['status'] = recent_queries['success'].map({True: '✅ Success', False: '❌ Failed'})
        recent_queries['query_preview'] = recent_queries['query'].str[:50] + '...'
        
        display_df = recent_queries[['timestamp', 'query_preview', 'response_time', 'status']].copy()
        display_df.columns = ['Timestamp', 'Query', 'Response Time (s)', 'Status']
        
        st.dataframe(display_df, use_container_width=True)
    
    else:
        st.info("📊 No query data available yet. Start using PandasAI to see analytics!")

def render_dataset_analytics():
    """Render dataset-specific analytics"""
    
    st.markdown("## 📊 Dataset Analytics")
    
    if st.session_state.current_dataframe is None:
        st.info("📊 Load a dataset to see analytics")
        return
    
    df = st.session_state.current_dataframe
    
    # Dataset metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Dataset Size", f"{df.shape[0]:,} × {df.shape[1]}")
    
    with col2:
        memory_usage = df.memory_usage(deep=True).sum() / (1024**2)
        st.metric("Memory Usage", f"{memory_usage:.2f} MB")
    
    with col3:
        missing_cells = df.isnull().sum().sum()
        total_cells = df.shape[0] * df.shape[1]
        missing_pct = (missing_cells / total_cells) * 100
        st.metric("Missing Data", f"{missing_pct:.1f}%")
    
    with col4:
        numeric_cols = len(df.select_dtypes(include=[np.number]).columns)
        st.metric("Numeric Columns", f"{numeric_cols}/{df.shape[1]}")
    
    # Column analysis
    st.markdown("### Column Analysis")
    
    column_info = []
    for col in df.columns:
        col_info = {
            "Column": col,
            "Type": str(df[col].dtype),
            "Non-Null Count": df[col].count(),
            "Null Count": df[col].isnull().sum(),
            "Unique Values": df[col].nunique(),
            "Memory (KB)": df[col].memory_usage(deep=True) / 1024
        }
        column_info.append(col_info)
    
    column_df = pd.DataFrame(column_info)
    st.dataframe(column_df, use_container_width=True)
    
    # Data type distribution
    dtype_counts = df.dtypes.value_counts()
    
    fig = px.bar(
        x=dtype_counts.index.astype(str),
        y=dtype_counts.values,
        title="Data Type Distribution",
        labels={'x': 'Data Type', 'y': 'Count'}
    )
    st.plotly_chart(fig, use_container_width=True)

def render_alerts_and_warnings():
    """Render system alerts and warnings"""
    
    st.markdown("## ⚠️ Alerts & Warnings")
    
    alerts = []
    
    # System alerts
    current_metrics = get_system_metrics()
    if current_metrics:
        if current_metrics['cpu_percent'] > 80:
            alerts.append(("🔴 High CPU Usage", f"CPU usage is at {current_metrics['cpu_percent']:.1f}%"))
        
        if current_metrics['memory_percent'] > 85:
            alerts.append(("🔴 High Memory Usage", f"Memory usage is at {current_metrics['memory_percent']:.1f}%"))
        
        if current_metrics['disk_percent'] > 90:
            alerts.append(("🔴 Low Disk Space", f"Disk usage is at {current_metrics['disk_percent']:.1f}%"))
    
    # PandasAI alerts
    analytics = st.session_state.chat_analytics
    if analytics["total_queries"] > 0:
        success_rate = (analytics["successful_queries"] / analytics["total_queries"]) * 100
        
        if success_rate < 70:
            alerts.append(("🟡 Low Success Rate", f"PandasAI success rate is {success_rate:.1f}%"))
        
        if analytics["average_response_time"] > 10:
            alerts.append(("🟡 Slow Response Time", f"Average response time is {analytics['average_response_time']:.2f}s"))
    
    # Dataset alerts
    if st.session_state.current_dataframe is not None:
        df = st.session_state.current_dataframe
        memory_usage = df.memory_usage(deep=True).sum() / (1024**2)
        
        if memory_usage > 500:  # 500 MB
            alerts.append(("🟡 Large Dataset", f"Dataset is using {memory_usage:.1f} MB of memory"))
        
        missing_pct = (df.isnull().sum().sum() / (df.shape[0] * df.shape[1])) * 100
        if missing_pct > 20:
            alerts.append(("🟡 High Missing Data", f"{missing_pct:.1f}% of data is missing"))
    
    # Display alerts
    if alerts:
        for alert_type, message in alerts:
            if "🔴" in alert_type:
                st.error(f"{alert_type}: {message}")
            elif "🟡" in alert_type:
                st.warning(f"{alert_type}: {message}")
            else:
                st.info(f"{alert_type}: {message}")
    else:
        st.success("✅ No alerts - system is running normally")

def render_export_monitoring_data():
    """Render export options for monitoring data"""
    
    st.markdown("## 📥 Export Monitoring Data")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("📊 Export System Metrics"):
            if st.session_state.system_metrics:
                metrics_df = pd.DataFrame(st.session_state.system_metrics)
                csv = metrics_df.to_csv(index=False)
                
                st.download_button(
                    label="Download System Metrics CSV",
                    data=csv,
                    file_name=f"system_metrics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    mime="text/csv"
                )
            else:
                st.warning("No system metrics to export")
    
    with col2:
        if st.button("🤖 Export PandasAI Analytics"):
            if st.session_state.chat_analytics["query_history"]:
                analytics_df = pd.DataFrame(st.session_state.chat_analytics["query_history"])
                csv = analytics_df.to_csv(index=False)
                
                st.download_button(
                    label="Download Analytics CSV",
                    data=csv,
                    file_name=f"pandasai_analytics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    mime="text/csv"
                )
            else:
                st.warning("No analytics data to export")
    
    with col3:
        if st.button("📋 Export Full Report"):
            # Create comprehensive monitoring report
            report = {
                "generated_at": datetime.now().isoformat(),
                "system_metrics": st.session_state.system_metrics,
                "chat_analytics": st.session_state.chat_analytics,
                "dataset_info": {
                    "shape": st.session_state.current_dataframe.shape if st.session_state.current_dataframe is not None else None,
                    "memory_usage": st.session_state.current_dataframe.memory_usage(deep=True).sum() / (1024**2) if st.session_state.current_dataframe is not None else None
                }
            }
            
            report_json = json.dumps(report, indent=2, default=str)
            
            st.download_button(
                label="Download Full Report JSON",
                data=report_json,
                file_name=f"monitoring_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                mime="application/json"
            )

def main():
    """Main function for System Monitoring page"""
    
    # Initialize session state
    initialize_session_state()
    
    # Page header
    st.markdown("# 📊 System Monitoring & Performance Analytics")
    st.markdown("Monitor PandasAI performance and system resources in real-time!")
    
    # Main monitoring tabs
    metrics_tab, performance_tab, analytics_tab, alerts_tab, export_tab = st.tabs([
        "📊 Real-Time Metrics", "📈 Performance", "🤖 PandasAI Analytics", "⚠️ Alerts", "📥 Export"
    ])
    
    with metrics_tab:
        render_real_time_metrics()
        render_dataset_analytics()
    
    with performance_tab:
        render_performance_charts()
    
    with analytics_tab:
        render_pandasai_analytics()
    
    with alerts_tab:
        render_alerts_and_warnings()
    
    with export_tab:
        render_export_monitoring_data()
    
    # Sidebar with monitoring controls
    with st.sidebar:
        st.markdown("## 🔧 Monitoring Controls")
        
        # Clear data buttons
        if st.button("🗑️ Clear System Metrics"):
            st.session_state.system_metrics = []
            st.success("System metrics cleared!")
        
        if st.button("🗑️ Clear Analytics Data"):
            st.session_state.chat_analytics = {
                "total_queries": 0,
                "successful_queries": 0,
                "failed_queries": 0,
                "average_response_time": 0,
                "query_history": []
            }
            st.success("Analytics data cleared!")
        
        # Monitoring status
        st.markdown("## 📊 Status")
        
        if st.session_state.monitoring_active:
            st.success("🟢 Real-time monitoring active")
        else:
            st.info("⚪ Real-time monitoring inactive")
        
        st.metric("Metrics Collected", len(st.session_state.system_metrics))
        st.metric("Queries Tracked", st.session_state.chat_analytics["total_queries"])

if __name__ == "__main__":
    main()
