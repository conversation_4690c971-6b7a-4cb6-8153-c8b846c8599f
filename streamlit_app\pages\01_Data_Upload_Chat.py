"""
Data Upload & PandasAI Chat Interface Page
"""
import streamlit as st
import pandas as pd
import numpy as np
import os
import sys
from io import StringIO
import json

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from components.pandasai_chat import PandasAIChatInterface
from components.ai_integration import AIIntegrationManager
from config.pandasai_config import PandasAIConfig

# Page configuration
st.set_page_config(
    page_title="Data Upload & Chat - AI Data Explorer",
    page_icon="📤",
    layout="wide"
)

def initialize_session_state():
    """Initialize session state for this page"""
    if 'api_key' not in st.session_state:
        st.session_state.api_key = ""
    
    if 'current_dataframe' not in st.session_state:
        st.session_state.current_dataframe = None
    
    if 'chat_interface' not in st.session_state:
        st.session_state.chat_interface = None
    
    if 'ai_integration' not in st.session_state:
        st.session_state.ai_integration = None
    
    if 'upload_history' not in st.session_state:
        st.session_state.upload_history = []

def load_sample_data():
    """Generate sample datasets for demonstration"""
    
    sample_datasets = {
        "Sales Data": pd.DataFrame({
            'date': pd.date_range('2023-01-01', periods=100, freq='D'),
            'product': np.random.choice(['Product A', 'Product B', 'Product C'], 100),
            'sales': np.random.randint(100, 1000, 100),
            'region': np.random.choice(['North', 'South', 'East', 'West'], 100),
            'customer_satisfaction': np.random.uniform(1, 5, 100)
        }),
        
        "Customer Data": pd.DataFrame({
            'customer_id': range(1, 201),
            'age': np.random.randint(18, 80, 200),
            'income': np.random.randint(30000, 150000, 200),
            'spending_score': np.random.randint(1, 100, 200),
            'gender': np.random.choice(['Male', 'Female'], 200),
            'city': np.random.choice(['New York', 'Los Angeles', 'Chicago', 'Houston'], 200)
        }),
        
        "Financial Data": pd.DataFrame({
            'stock_symbol': np.random.choice(['AAPL', 'GOOGL', 'MSFT', 'AMZN'], 150),
            'price': np.random.uniform(100, 500, 150),
            'volume': np.random.randint(1000000, 10000000, 150),
            'market_cap': np.random.uniform(1e9, 1e12, 150),
            'pe_ratio': np.random.uniform(10, 50, 150),
            'sector': np.random.choice(['Technology', 'Healthcare', 'Finance', 'Energy'], 150)
        })
    }
    
    return sample_datasets

def validate_uploaded_file(uploaded_file) -> tuple:
    """Validate uploaded file and return dataframe and error message"""
    
    try:
        file_extension = uploaded_file.name.split('.')[-1].lower()
        
        if file_extension == 'csv':
            df = pd.read_csv(uploaded_file)
        elif file_extension in ['xlsx', 'xls']:
            df = pd.read_excel(uploaded_file)
        elif file_extension == 'json':
            df = pd.read_json(uploaded_file)
        elif file_extension == 'parquet':
            df = pd.read_parquet(uploaded_file)
        else:
            return None, f"Unsupported file format: {file_extension}"
        
        # Basic validation
        if df.empty:
            return None, "The uploaded file is empty"
        
        if len(df.columns) == 0:
            return None, "The uploaded file has no columns"
        
        # Check file size (limit to reasonable size for demo)
        if len(df) > 100000:
            return None, f"File too large: {len(df)} rows. Please limit to 100,000 rows."
        
        return df, None
        
    except Exception as e:
        return None, f"Error reading file: {str(e)}"

def render_file_upload_section():
    """Render the file upload section"""
    
    st.markdown("## 📤 Data Upload")
    
    # File upload tabs
    upload_tab, sample_tab = st.tabs(["📁 Upload File", "🎯 Sample Data"])
    
    with upload_tab:
        st.markdown("### Upload Your Dataset")
        
        uploaded_file = st.file_uploader(
            "Choose a file",
            type=['csv', 'xlsx', 'xls', 'json', 'parquet'],
            help="Supported formats: CSV, Excel, JSON, Parquet (Max 100,000 rows)"
        )
        
        if uploaded_file is not None:
            with st.spinner("Processing uploaded file..."):
                df, error = validate_uploaded_file(uploaded_file)
                
                if error:
                    st.error(f"❌ {error}")
                else:
                    st.success(f"✅ Successfully loaded {uploaded_file.name}")
                    
                    # Store in session state
                    st.session_state.current_dataframe = df
                    
                    # Add to upload history
                    upload_info = {
                        "filename": uploaded_file.name,
                        "shape": df.shape,
                        "upload_time": pd.Timestamp.now(),
                        "size_mb": uploaded_file.size / (1024 * 1024)
                    }
                    st.session_state.upload_history.append(upload_info)
                    
                    # Initialize AI components if API key is available
                    if st.session_state.api_key:
                        initialize_ai_components(df)
                    
                    st.rerun()
    
    with sample_tab:
        st.markdown("### Try Sample Datasets")
        
        sample_datasets = load_sample_data()
        
        col1, col2, col3 = st.columns(3)
        
        for i, (name, df) in enumerate(sample_datasets.items()):
            col = [col1, col2, col3][i % 3]
            
            with col:
                st.markdown(f"**{name}**")
                st.markdown(f"Shape: {df.shape[0]} rows × {df.shape[1]} columns")
                
                if st.button(f"Load {name}", key=f"load_sample_{i}"):
                    st.session_state.current_dataframe = df
                    
                    # Add to upload history
                    upload_info = {
                        "filename": f"{name} (Sample)",
                        "shape": df.shape,
                        "upload_time": pd.Timestamp.now(),
                        "size_mb": df.memory_usage(deep=True).sum() / (1024 * 1024)
                    }
                    st.session_state.upload_history.append(upload_info)
                    
                    # Initialize AI components if API key is available
                    if st.session_state.api_key:
                        initialize_ai_components(df)
                    
                    st.success(f"✅ Loaded {name}")
                    st.rerun()

def initialize_ai_components(dataframe):
    """Initialize AI components with the loaded dataframe"""
    
    try:
        # Initialize AI integration
        if st.session_state.api_key:
            st.session_state.ai_integration = AIIntegrationManager(st.session_state.api_key)
            st.session_state.ai_integration.initialize_pandasai(dataframe)
            
            # Initialize chat interface
            st.session_state.chat_interface = PandasAIChatInterface("upload_page_chat")
            st.session_state.chat_interface.initialize_client(st.session_state.api_key, dataframe)
            
            st.success("🤖 AI components initialized successfully!")
        else:
            st.warning("⚠️ Please set your API key in the sidebar to enable AI features")
            
    except Exception as e:
        st.error(f"❌ Failed to initialize AI components: {str(e)}")

def render_dataset_preview():
    """Render dataset preview and information"""
    
    if st.session_state.current_dataframe is None:
        st.info("📊 Upload a dataset to see preview and start chatting")
        return
    
    df = st.session_state.current_dataframe
    
    st.markdown("## 📊 Dataset Overview")
    
    # Dataset metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Rows", f"{df.shape[0]:,}")
    
    with col2:
        st.metric("Columns", df.shape[1])
    
    with col3:
        st.metric("Missing Values", f"{df.isnull().sum().sum():,}")
    
    with col4:
        memory_mb = df.memory_usage(deep=True).sum() / (1024 * 1024)
        st.metric("Memory Usage", f"{memory_mb:.2f} MB")
    
    # Dataset preview tabs
    preview_tab, info_tab, stats_tab = st.tabs(["👀 Preview", "ℹ️ Info", "📈 Statistics"])
    
    with preview_tab:
        st.markdown("### First 10 Rows")
        st.dataframe(df.head(10), use_container_width=True)
        
        st.markdown("### Last 5 Rows")
        st.dataframe(df.tail(5), use_container_width=True)
    
    with info_tab:
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("**Column Information:**")
            info_df = pd.DataFrame({
                'Column': df.columns,
                'Type': df.dtypes.astype(str),
                'Non-Null Count': df.count(),
                'Null Count': df.isnull().sum()
            })
            st.dataframe(info_df, use_container_width=True)
        
        with col2:
            st.markdown("**Data Types Summary:**")
            dtype_counts = df.dtypes.value_counts()
            st.bar_chart(dtype_counts)
    
    with stats_tab:
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        
        if len(numeric_cols) > 0:
            st.markdown("**Numerical Statistics:**")
            st.dataframe(df[numeric_cols].describe(), use_container_width=True)
        
        categorical_cols = df.select_dtypes(include=['object', 'category']).columns
        
        if len(categorical_cols) > 0:
            st.markdown("**Categorical Statistics:**")
            cat_stats = pd.DataFrame({
                'Column': categorical_cols,
                'Unique Values': [df[col].nunique() for col in categorical_cols],
                'Most Frequent': [df[col].mode().iloc[0] if len(df[col].mode()) > 0 else 'N/A' for col in categorical_cols]
            })
            st.dataframe(cat_stats, use_container_width=True)

def render_api_configuration():
    """Render API configuration section"""
    
    with st.sidebar:
        st.markdown("## 🔑 API Configuration")
        
        api_key = st.text_input(
            "Gemini API Key:",
            value=st.session_state.api_key,
            type="password",
            help="Enter your Google Gemini API key"
        )
        
        if api_key != st.session_state.api_key:
            st.session_state.api_key = api_key
            
            # Reinitialize AI components if dataframe is loaded
            if st.session_state.current_dataframe is not None and api_key:
                initialize_ai_components(st.session_state.current_dataframe)
            
            st.rerun()
        
        # API status
        if st.session_state.api_key:
            st.success("✅ API Key Set")
        else:
            st.warning("⚠️ API Key Required")
        
        # Upload history
        if st.session_state.upload_history:
            st.markdown("## 📁 Upload History")
            
            for i, upload in enumerate(st.session_state.upload_history[-5:]):  # Show last 5
                with st.expander(f"{upload['filename']}"):
                    st.write(f"**Shape:** {upload['shape'][0]} × {upload['shape'][1]}")
                    st.write(f"**Size:** {upload['size_mb']:.2f} MB")
                    st.write(f"**Time:** {upload['upload_time'].strftime('%Y-%m-%d %H:%M')}")

def main():
    """Main function for the Data Upload & Chat page"""
    
    # Initialize session state
    initialize_session_state()
    
    # Page header
    st.markdown("# 📤 Data Upload & PandasAI Chat Interface")
    st.markdown("Upload your dataset and start conversing with your data using natural language!")
    
    # Render API configuration in sidebar
    render_api_configuration()
    
    # Main content layout
    upload_col, chat_col = st.columns([1, 1])
    
    with upload_col:
        render_file_upload_section()
        render_dataset_preview()
    
    with chat_col:
        st.markdown("## 💬 PandasAI Chat Interface")
        
        if st.session_state.chat_interface and st.session_state.current_dataframe is not None:
            st.session_state.chat_interface.render_chat_interface(height=800)
        else:
            st.info("🤖 Upload a dataset and configure your API key to start chatting!")
            
            # Show configuration status
            config_status = []
            if st.session_state.api_key:
                config_status.append("✅ API Key configured")
            else:
                config_status.append("❌ API Key missing")
            
            if st.session_state.current_dataframe is not None:
                config_status.append("✅ Dataset loaded")
            else:
                config_status.append("❌ No dataset loaded")
            
            for status in config_status:
                st.markdown(status)

if __name__ == "__main__":
    main()
