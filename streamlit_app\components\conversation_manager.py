"""
Conversation Manager for PandasAI Chat Interface
"""
import streamlit as st
import pandas as pd
from typing import Dict, List, Any, Optional
import json
import uuid
from datetime import datetime

class ConversationManager:
    """Manages conversation state and history for PandasAI chat interface"""
    
    def __init__(self, session_key: str = "conversation_manager"):
        self.session_key = session_key
        self._initialize_session_state()
    
    def _initialize_session_state(self):
        """Initialize session state variables"""
        if self.session_key not in st.session_state:
            st.session_state[self.session_key] = {
                "conversations": {},
                "current_conversation_id": None,
                "conversation_counter": 0
            }
    
    def create_new_conversation(self, name: str = None) -> str:
        """Create a new conversation and return its ID"""
        conversation_id = str(uuid.uuid4())
        
        if name is None:
            st.session_state[self.session_key]["conversation_counter"] += 1
            name = f"Conversation {st.session_state[self.session_key]['conversation_counter']}"
        
        st.session_state[self.session_key]["conversations"][conversation_id] = {
            "id": conversation_id,
            "name": name,
            "created_at": datetime.now().isoformat(),
            "messages": [],
            "dataset_info": None,
            "metadata": {}
        }
        
        st.session_state[self.session_key]["current_conversation_id"] = conversation_id
        return conversation_id
    
    def get_current_conversation_id(self) -> Optional[str]:
        """Get the current conversation ID"""
        return st.session_state[self.session_key].get("current_conversation_id")
    
    def set_current_conversation(self, conversation_id: str):
        """Set the current conversation"""
        if conversation_id in st.session_state[self.session_key]["conversations"]:
            st.session_state[self.session_key]["current_conversation_id"] = conversation_id
    
    def get_conversation(self, conversation_id: str = None) -> Optional[Dict[str, Any]]:
        """Get conversation by ID or current conversation"""
        if conversation_id is None:
            conversation_id = self.get_current_conversation_id()
        
        if conversation_id is None:
            return None
            
        return st.session_state[self.session_key]["conversations"].get(conversation_id)
    
    def add_message(self, message_type: str, content: str, metadata: Dict[str, Any] = None, conversation_id: str = None):
        """Add a message to the conversation"""
        if conversation_id is None:
            conversation_id = self.get_current_conversation_id()
        
        if conversation_id is None:
            # Create new conversation if none exists
            conversation_id = self.create_new_conversation()
        
        message = {
            "id": str(uuid.uuid4()),
            "type": message_type,  # 'user', 'assistant', 'system', 'error'
            "content": content,
            "timestamp": datetime.now().isoformat(),
            "metadata": metadata or {}
        }
        
        st.session_state[self.session_key]["conversations"][conversation_id]["messages"].append(message)
    
    def get_messages(self, conversation_id: str = None) -> List[Dict[str, Any]]:
        """Get all messages from a conversation"""
        conversation = self.get_conversation(conversation_id)
        if conversation:
            return conversation["messages"]
        return []
    
    def update_dataset_info(self, dataset_info: Dict[str, Any], conversation_id: str = None):
        """Update dataset information for the conversation"""
        if conversation_id is None:
            conversation_id = self.get_current_conversation_id()
        
        if conversation_id and conversation_id in st.session_state[self.session_key]["conversations"]:
            st.session_state[self.session_key]["conversations"][conversation_id]["dataset_info"] = dataset_info
    
    def get_dataset_info(self, conversation_id: str = None) -> Optional[Dict[str, Any]]:
        """Get dataset information for the conversation"""
        conversation = self.get_conversation(conversation_id)
        if conversation:
            return conversation.get("dataset_info")
        return None
    
    def get_all_conversations(self) -> Dict[str, Dict[str, Any]]:
        """Get all conversations"""
        return st.session_state[self.session_key]["conversations"]
    
    def delete_conversation(self, conversation_id: str):
        """Delete a conversation"""
        if conversation_id in st.session_state[self.session_key]["conversations"]:
            del st.session_state[self.session_key]["conversations"][conversation_id]
            
            # If this was the current conversation, clear it
            if st.session_state[self.session_key]["current_conversation_id"] == conversation_id:
                st.session_state[self.session_key]["current_conversation_id"] = None
    
    def rename_conversation(self, conversation_id: str, new_name: str):
        """Rename a conversation"""
        if conversation_id in st.session_state[self.session_key]["conversations"]:
            st.session_state[self.session_key]["conversations"][conversation_id]["name"] = new_name
    
    def export_conversation(self, conversation_id: str = None) -> str:
        """Export conversation as JSON string"""
        conversation = self.get_conversation(conversation_id)
        if conversation:
            return json.dumps(conversation, indent=2, default=str)
        return "{}"
    
    def import_conversation(self, conversation_data: str) -> bool:
        """Import conversation from JSON string"""
        try:
            data = json.loads(conversation_data)
            conversation_id = data.get("id", str(uuid.uuid4()))
            
            # Ensure required fields exist
            if "messages" not in data:
                data["messages"] = []
            if "created_at" not in data:
                data["created_at"] = datetime.now().isoformat()
            if "name" not in data:
                data["name"] = f"Imported Conversation"
            
            st.session_state[self.session_key]["conversations"][conversation_id] = data
            return True
        except Exception as e:
            st.error(f"Failed to import conversation: {str(e)}")
            return False
    
    def clear_all_conversations(self):
        """Clear all conversations"""
        st.session_state[self.session_key] = {
            "conversations": {},
            "current_conversation_id": None,
            "conversation_counter": 0
        }
    
    def get_conversation_summary(self, conversation_id: str = None) -> Dict[str, Any]:
        """Get a summary of the conversation"""
        conversation = self.get_conversation(conversation_id)
        if not conversation:
            return {}
        
        messages = conversation["messages"]
        user_messages = [m for m in messages if m["type"] == "user"]
        assistant_messages = [m for m in messages if m["type"] == "assistant"]
        error_messages = [m for m in messages if m["type"] == "error"]
        
        return {
            "name": conversation["name"],
            "created_at": conversation["created_at"],
            "total_messages": len(messages),
            "user_messages": len(user_messages),
            "assistant_messages": len(assistant_messages),
            "error_messages": len(error_messages),
            "has_dataset": conversation.get("dataset_info") is not None,
            "last_activity": messages[-1]["timestamp"] if messages else conversation["created_at"]
        }
