"""
AI Integration Component - Coordinates PandasAI with Gemini 2.5
"""
import streamlit as st
import pandas as pd
import google.generativeai as genai
from typing import Dict, List, Any, Optional, Tuple
import json
import logging

from ..utils.pandasai_client import PandasAIClient
from ..config.pandasai_config import PandasAIConfig

logger = logging.getLogger(__name__)

class AIIntegrationManager:
    """Manages integration between PandasAI and Gemini 2.5 Flash"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.config = PandasAIConfig.get_config()
        self.pandasai_client = None
        self.gemini_model = None
        
        # Initialize Gemini
        if api_key:
            genai.configure(api_key=api_key)
            self.gemini_model = genai.GenerativeModel('gemini-2.5-flash')
    
    def initialize_pandasai(self, dataframe: pd.DataFrame) -> bool:
        """Initialize PandasAI client with dataframe"""
        try:
            self.pandasai_client = PandasAIClient(self.api_key, self.config["pandasai"])
            return self.pandasai_client.initialize_agent(dataframe)
        except Exception as e:
            logger.error(f"Failed to initialize PandasAI: {str(e)}")
            return False
    
    def get_comprehensive_analysis(self, dataframe: pd.DataFrame, user_query: str) -> Dict[str, Any]:
        """
        Get comprehensive analysis combining PandasAI and Gemini insights
        
        Args:
            dataframe: The dataset to analyze
            user_query: User's analysis request
            
        Returns:
            Dictionary containing combined insights
        """
        
        results = {
            "pandasai_response": None,
            "pandasai_code": None,
            "gemini_insights": None,
            "combined_summary": None,
            "recommendations": [],
            "visualizations": []
        }
        
        try:
            # Get PandasAI response
            if self.pandasai_client:
                pandasai_response, code, chart = self.pandasai_client.chat(user_query)
                results["pandasai_response"] = pandasai_response
                results["pandasai_code"] = code
                if chart:
                    results["visualizations"].append({"type": "pandasai", "path": chart})
            
            # Get Gemini analysis
            gemini_insights = self._get_gemini_analysis(dataframe, user_query, results["pandasai_response"])
            results["gemini_insights"] = gemini_insights
            
            # Generate combined summary
            combined_summary = self._generate_combined_summary(results)
            results["combined_summary"] = combined_summary
            
            # Generate recommendations
            recommendations = self._generate_recommendations(dataframe, user_query, results)
            results["recommendations"] = recommendations
            
        except Exception as e:
            logger.error(f"Error in comprehensive analysis: {str(e)}")
            results["error"] = str(e)
        
        return results
    
    def _get_gemini_analysis(self, dataframe: pd.DataFrame, user_query: str, pandasai_response: str = None) -> str:
        """Get analysis from Gemini 2.5 Flash"""
        
        if not self.gemini_model:
            return "Gemini analysis unavailable - API key required"
        
        try:
            # Prepare dataset summary for Gemini
            dataset_summary = self._prepare_dataset_summary(dataframe)
            
            # Create prompt for Gemini
            prompt = f"""
            As a data science expert, analyze this dataset and provide insights for the user query.
            
            Dataset Summary:
            {dataset_summary}
            
            User Query: {user_query}
            
            {f"PandasAI Response: {pandasai_response}" if pandasai_response else ""}
            
            Please provide:
            1. Statistical insights and patterns
            2. Data quality assessment
            3. Potential issues or anomalies
            4. Business implications
            5. Suggested next steps for analysis
            
            Focus on actionable insights and be specific about findings.
            """
            
            response = self.gemini_model.generate_content(prompt)
            return response.text
            
        except Exception as e:
            logger.error(f"Gemini analysis error: {str(e)}")
            return f"Gemini analysis error: {str(e)}"
    
    def _prepare_dataset_summary(self, dataframe: pd.DataFrame) -> str:
        """Prepare a concise dataset summary for Gemini"""
        
        summary = f"""
        Dataset Shape: {dataframe.shape[0]} rows, {dataframe.shape[1]} columns
        
        Columns and Types:
        {dataframe.dtypes.to_string()}
        
        Missing Values:
        {dataframe.isnull().sum().to_string()}
        
        Numerical Summary:
        {dataframe.describe().to_string() if len(dataframe.select_dtypes(include=['number']).columns) > 0 else "No numerical columns"}
        
        Categorical Summary:
        {dataframe.select_dtypes(include=['object', 'category']).nunique().to_string() if len(dataframe.select_dtypes(include=['object', 'category']).columns) > 0 else "No categorical columns"}
        """
        
        return summary
    
    def _generate_combined_summary(self, results: Dict[str, Any]) -> str:
        """Generate a combined summary from both AI responses"""
        
        if not self.gemini_model:
            return results.get("pandasai_response", "No analysis available")
        
        try:
            prompt = f"""
            Combine and synthesize these AI analysis results into a coherent summary:
            
            PandasAI Response: {results.get('pandasai_response', 'Not available')}
            
            Gemini Insights: {results.get('gemini_insights', 'Not available')}
            
            Create a unified summary that:
            1. Highlights key findings from both analyses
            2. Resolves any contradictions
            3. Provides a clear, actionable conclusion
            4. Suggests follow-up questions or analyses
            
            Keep it concise but comprehensive.
            """
            
            response = self.gemini_model.generate_content(prompt)
            return response.text
            
        except Exception as e:
            logger.error(f"Combined summary error: {str(e)}")
            return f"Summary generation error: {str(e)}"
    
    def _generate_recommendations(self, dataframe: pd.DataFrame, user_query: str, results: Dict[str, Any]) -> List[str]:
        """Generate actionable recommendations"""
        
        if not self.gemini_model:
            return ["Upload dataset and configure API key for recommendations"]
        
        try:
            prompt = f"""
            Based on this data analysis, provide 5 specific, actionable recommendations:
            
            User Query: {user_query}
            Analysis Results: {results.get('combined_summary', 'No analysis available')}
            
            Recommendations should be:
            1. Specific and actionable
            2. Relevant to the user's query
            3. Based on the data findings
            4. Prioritized by importance
            5. Include next steps for implementation
            
            Format as a numbered list.
            """
            
            response = self.gemini_model.generate_content(prompt)
            
            # Parse recommendations into list
            recommendations_text = response.text
            recommendations = []
            
            for line in recommendations_text.split('\n'):
                line = line.strip()
                if line and (line[0].isdigit() or line.startswith('-') or line.startswith('•')):
                    # Clean up the line
                    clean_line = line.lstrip('0123456789.-• ').strip()
                    if clean_line:
                        recommendations.append(clean_line)
            
            return recommendations[:5]  # Limit to 5 recommendations
            
        except Exception as e:
            logger.error(f"Recommendations error: {str(e)}")
            return [f"Recommendation generation error: {str(e)}"]
    
    def get_feature_engineering_suggestions(self, dataframe: pd.DataFrame) -> Dict[str, List[str]]:
        """Get AI-powered feature engineering suggestions"""
        
        if not self.gemini_model:
            return {"error": ["Gemini API key required for feature engineering suggestions"]}
        
        try:
            dataset_summary = self._prepare_dataset_summary(dataframe)
            
            prompt = f"""
            Analyze this dataset and suggest feature engineering opportunities:
            
            {dataset_summary}
            
            Provide suggestions in these categories:
            1. Interaction Features: Features created by combining existing features
            2. Transformation Features: Mathematical transformations of existing features
            3. Aggregation Features: Summary statistics or grouping-based features
            4. Temporal Features: Time-based feature extraction (if applicable)
            5. Categorical Features: Encoding or binning suggestions
            
            For each suggestion, explain the rationale and potential impact.
            Format as JSON with categories as keys and lists of suggestions as values.
            """
            
            response = self.gemini_model.generate_content(prompt)
            
            # Try to parse as JSON, fallback to text parsing
            try:
                suggestions = json.loads(response.text)
            except:
                # Fallback text parsing
                suggestions = {
                    "interaction_features": [],
                    "transformation_features": [],
                    "aggregation_features": [],
                    "temporal_features": [],
                    "categorical_features": []
                }
                
                current_category = None
                for line in response.text.split('\n'):
                    line = line.strip()
                    if 'interaction' in line.lower():
                        current_category = 'interaction_features'
                    elif 'transformation' in line.lower():
                        current_category = 'transformation_features'
                    elif 'aggregation' in line.lower():
                        current_category = 'aggregation_features'
                    elif 'temporal' in line.lower():
                        current_category = 'temporal_features'
                    elif 'categorical' in line.lower():
                        current_category = 'categorical_features'
                    elif line and current_category and (line.startswith('-') or line.startswith('•')):
                        suggestions[current_category].append(line.lstrip('-• ').strip())
            
            return suggestions
            
        except Exception as e:
            logger.error(f"Feature engineering suggestions error: {str(e)}")
            return {"error": [f"Feature engineering suggestions error: {str(e)}"]}
    
    def validate_ai_responses(self, pandasai_response: str, gemini_response: str) -> Dict[str, Any]:
        """Cross-validate responses from both AI systems"""
        
        validation_result = {
            "consistency_score": 0.0,
            "conflicts": [],
            "agreements": [],
            "confidence_level": "low"
        }
        
        try:
            if not self.gemini_model:
                return validation_result
            
            prompt = f"""
            Compare these two AI responses for consistency and accuracy:
            
            PandasAI Response: {pandasai_response}
            Gemini Response: {gemini_response}
            
            Analyze:
            1. Are the responses consistent with each other?
            2. Are there any contradictions?
            3. What points do they agree on?
            4. Overall confidence level (high/medium/low)
            
            Provide a structured analysis.
            """
            
            response = self.gemini_model.generate_content(prompt)
            
            # Simple parsing for validation metrics
            response_text = response.text.lower()
            
            if "consistent" in response_text or "agree" in response_text:
                validation_result["consistency_score"] = 0.8
                validation_result["confidence_level"] = "high"
            elif "some differences" in response_text or "partially" in response_text:
                validation_result["consistency_score"] = 0.5
                validation_result["confidence_level"] = "medium"
            else:
                validation_result["consistency_score"] = 0.2
                validation_result["confidence_level"] = "low"
            
            validation_result["analysis"] = response.text
            
        except Exception as e:
            logger.error(f"Validation error: {str(e)}")
            validation_result["error"] = str(e)
        
        return validation_result
