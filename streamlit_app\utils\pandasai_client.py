"""
PandasAI Client Integration
"""
import os
import pandas as pd
import streamlit as st
from typing import Dict, Any, Optional, List, Tuple
import google.generativeai as genai
from pandasai import Agent
from pandasai.llm import GoogleGemini
from pandasai.responses.response_parser import ResponseParser
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PandasAIClient:
    """Enhanced PandasAI client with Gemini 2.5 Flash integration"""
    
    def __init__(self, api_key: str, config: Dict[str, Any] = None):
        """Initialize PandasAI client with configuration"""
        self.api_key = api_key
        self.config = config or {}
        self.agent = None
        self.conversation_history = []
        self.current_dataframe = None
        
        # Initialize Gemini
        if api_key:
            genai.configure(api_key=api_key)
            self.llm = GoogleGemini(
                api_key=api_key,
                model="gemini-2.5-flash",
                temperature=self.config.get("temperature", 0.1)
            )
        else:
            self.llm = None
            logger.warning("No API key provided. PandasAI functionality will be limited.")
    
    def initialize_agent(self, dataframe: pd.DataFrame) -> bool:
        """Initialize PandasAI agent with dataframe"""
        try:
            if self.llm is None:
                st.error("API key required for PandasAI functionality")
                return False
                
            self.current_dataframe = dataframe
            self.agent = Agent(
                dfs=[dataframe],
                config={
                    "llm": self.llm,
                    "verbose": self.config.get("verbose", True),
                    "enforce_privacy": self.config.get("enforce_privacy", True),
                    "enable_cache": self.config.get("enable_cache", True),
                    "save_charts": self.config.get("save_charts", True),
                    "save_charts_path": self.config.get("save_charts_path", "charts/"),
                    "custom_whitelisted_dependencies": self.config.get(
                        "custom_whitelisted_dependencies", 
                        ["plotly", "seaborn", "matplotlib", "numpy", "pandas"]
                    )
                }
            )
            
            # Add initial context about the dataset
            self._add_dataset_context()
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize PandasAI agent: {str(e)}")
            st.error(f"Failed to initialize AI agent: {str(e)}")
            return False
    
    def _add_dataset_context(self):
        """Add initial context about the dataset to conversation history"""
        if self.current_dataframe is not None:
            context = {
                "type": "system",
                "content": f"Dataset loaded with {len(self.current_dataframe)} rows and {len(self.current_dataframe.columns)} columns. "
                          f"Columns: {', '.join(self.current_dataframe.columns.tolist())}"
            }
            self.conversation_history.append(context)
    
    def chat(self, query: str) -> Tuple[str, Optional[str], Optional[Any]]:
        """
        Send query to PandasAI and return response, code, and any generated chart
        
        Returns:
            Tuple of (response_text, generated_code, chart_path_or_object)
        """
        if self.agent is None:
            return "Please upload a dataset first to start the conversation.", None, None
        
        try:
            # Add user query to history
            self.conversation_history.append({
                "type": "user",
                "content": query,
                "timestamp": pd.Timestamp.now()
            })
            
            # Get response from PandasAI
            response = self.agent.chat(query)
            
            # Extract generated code if available
            generated_code = self._extract_generated_code()
            
            # Check for generated charts
            chart_path = self._check_for_generated_charts()
            
            # Add AI response to history
            self.conversation_history.append({
                "type": "assistant",
                "content": str(response),
                "code": generated_code,
                "chart": chart_path,
                "timestamp": pd.Timestamp.now()
            })
            
            return str(response), generated_code, chart_path
            
        except Exception as e:
            error_msg = f"Error processing query: {str(e)}"
            logger.error(error_msg)
            
            # Add error to history
            self.conversation_history.append({
                "type": "error",
                "content": error_msg,
                "timestamp": pd.Timestamp.now()
            })
            
            return error_msg, None, None
    
    def _extract_generated_code(self) -> Optional[str]:
        """Extract generated pandas code from the agent"""
        try:
            if hasattr(self.agent, 'last_code_generated'):
                return self.agent.last_code_generated
            return None
        except:
            return None
    
    def _check_for_generated_charts(self) -> Optional[str]:
        """Check if any charts were generated and return path"""
        try:
            charts_path = self.config.get("save_charts_path", "charts/")
            if os.path.exists(charts_path):
                chart_files = [f for f in os.listdir(charts_path) if f.endswith(('.png', '.jpg', '.svg', '.html'))]
                if chart_files:
                    # Return the most recent chart
                    chart_files.sort(key=lambda x: os.path.getmtime(os.path.join(charts_path, x)), reverse=True)
                    return os.path.join(charts_path, chart_files[0])
            return None
        except:
            return None
    
    def get_conversation_history(self) -> List[Dict[str, Any]]:
        """Get the conversation history"""
        return self.conversation_history
    
    def clear_conversation(self):
        """Clear conversation history"""
        self.conversation_history = []
        if self.current_dataframe is not None:
            self._add_dataset_context()
    
    def get_suggested_questions(self) -> List[str]:
        """Get suggested questions based on the current dataset"""
        if self.current_dataframe is None:
            return [
                "Upload a dataset to get started!",
                "What can PandasAI help you with?",
                "Try uploading a CSV, Excel, or JSON file"
            ]
        
        # Basic suggestions
        suggestions = [
            "What does this dataset contain?",
            "Show me basic statistics for all columns",
            "Are there any missing values?",
            "What are the data types of each column?"
        ]
        
        # Add column-specific suggestions
        numeric_cols = self.current_dataframe.select_dtypes(include=['number']).columns.tolist()
        categorical_cols = self.current_dataframe.select_dtypes(include=['object', 'category']).columns.tolist()
        
        if numeric_cols:
            suggestions.extend([
                f"Show me the correlation between {numeric_cols[0]} and other numerical columns",
                f"What's the distribution of {numeric_cols[0]}?",
                "Create a correlation heatmap for numerical columns"
            ])
        
        if categorical_cols:
            suggestions.extend([
                f"What are the unique values in {categorical_cols[0]}?",
                f"Show me the value counts for {categorical_cols[0]}"
            ])
        
        if len(numeric_cols) >= 2:
            suggestions.append(f"Create a scatter plot of {numeric_cols[0]} vs {numeric_cols[1]}")
        
        return suggestions[:8]  # Limit to 8 suggestions
    
    def export_conversation(self) -> str:
        """Export conversation history as formatted text"""
        if not self.conversation_history:
            return "No conversation history to export."
        
        export_text = "# PandasAI Conversation Export\n\n"
        export_text += f"Generated on: {pd.Timestamp.now()}\n\n"
        
        for i, message in enumerate(self.conversation_history, 1):
            if message["type"] == "user":
                export_text += f"## Query {i}\n**User:** {message['content']}\n\n"
            elif message["type"] == "assistant":
                export_text += f"**AI Response:** {message['content']}\n\n"
                if message.get("code"):
                    export_text += f"**Generated Code:**\n```python\n{message['code']}\n```\n\n"
            elif message["type"] == "error":
                export_text += f"**Error:** {message['content']}\n\n"
        
        return export_text
