/* Custom CSS for AI-Driven Data Explorer */

/* Main app styling */
.main {
    padding-top: 1rem;
}

/* Header styling */
.main-header {
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 10px;
    margin-bottom: 2rem;
    text-align: center;
}

.main-header h1 {
    margin: 0;
    font-size: 2.5rem;
    font-weight: 700;
}

.main-header p {
    margin: 0.5rem 0 0 0;
    font-size: 1.2rem;
    opacity: 0.9;
}

/* Chat interface styling */
.chat-container {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 1.5rem;
    margin: 1rem 0;
    border: 1px solid #e9ecef;
}

.chat-message {
    margin: 1rem 0;
    padding: 1rem;
    border-radius: 10px;
    max-width: 80%;
}

.chat-message.user {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin-left: auto;
    text-align: right;
}

.chat-message.assistant {
    background: #ffffff;
    border: 1px solid #e9ecef;
    margin-right: auto;
}

.chat-message.system {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    font-style: italic;
    text-align: center;
}

/* Button styling */
.stButton > button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 0.5rem 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.stButton > button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.stButton > button:active {
    transform: translateY(0);
}

/* Suggestion buttons */
.suggestion-button {
    background: #ffffff;
    border: 2px solid #667eea;
    color: #667eea;
    border-radius: 20px;
    padding: 0.5rem 1rem;
    margin: 0.25rem;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.suggestion-button:hover {
    background: #667eea;
    color: white;
    transform: scale(1.05);
}

/* Metric cards */
.metric-card {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #667eea;
    margin: 1rem 0;
}

.metric-card h3 {
    color: #667eea;
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
}

.metric-card .metric-value {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
}

.metric-card .metric-delta {
    font-size: 0.9rem;
    color: #7f8c8d;
    margin: 0.25rem 0 0 0;
}

/* Data preview tables */
.dataframe {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.dataframe thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: 600;
    padding: 1rem;
}

.dataframe tbody td {
    padding: 0.75rem;
    border-bottom: 1px solid #e9ecef;
}

.dataframe tbody tr:hover {
    background: #f8f9fa;
}

/* Sidebar styling */
.sidebar .sidebar-content {
    background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 10px;
    padding: 1rem;
}

/* Tab styling */
.stTabs [data-baseweb="tab-list"] {
    gap: 8px;
}

.stTabs [data-baseweb="tab"] {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 0.5rem 1rem;
    border: 1px solid #e9ecef;
    color: #495057;
    font-weight: 500;
}

.stTabs [aria-selected="true"] {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: 1px solid #667eea;
}

/* Alert styling */
.alert {
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
    border-left: 4px solid;
}

.alert.success {
    background: #d4edda;
    border-color: #28a745;
    color: #155724;
}

.alert.warning {
    background: #fff3cd;
    border-color: #ffc107;
    color: #856404;
}

.alert.error {
    background: #f8d7da;
    border-color: #dc3545;
    color: #721c24;
}

.alert.info {
    background: #d1ecf1;
    border-color: #17a2b8;
    color: #0c5460;
}

/* Code display styling */
.code-container {
    background: #2d3748;
    color: #e2e8f0;
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    overflow-x: auto;
}

.code-container .code-header {
    background: #4a5568;
    color: white;
    padding: 0.5rem 1rem;
    margin: -1rem -1rem 1rem -1rem;
    border-radius: 8px 8px 0 0;
    font-weight: 600;
    font-size: 0.9rem;
}

/* Chart container styling */
.chart-container {
    background: white;
    border-radius: 10px;
    padding: 1rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin: 1rem 0;
}

/* Upload area styling */
.upload-area {
    border: 2px dashed #667eea;
    border-radius: 10px;
    padding: 2rem;
    text-align: center;
    background: #f8f9fa;
    margin: 1rem 0;
    transition: all 0.3s ease;
}

.upload-area:hover {
    border-color: #764ba2;
    background: #f0f0f0;
}

.upload-area .upload-icon {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.upload-area .upload-text {
    color: #495057;
    font-size: 1.1rem;
    margin: 0;
}

/* Progress bar styling */
.progress-bar {
    background: #e9ecef;
    border-radius: 10px;
    height: 8px;
    overflow: hidden;
    margin: 1rem 0;
}

.progress-bar .progress-fill {
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    height: 100%;
    border-radius: 10px;
    transition: width 0.3s ease;
}

/* Feature card styling */
.feature-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin: 1rem 0;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.feature-card .feature-icon {
    font-size: 2rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.feature-card .feature-title {
    color: #2c3e50;
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
}

.feature-card .feature-description {
    color: #7f8c8d;
    font-size: 0.9rem;
    line-height: 1.5;
    margin: 0;
}

/* Status indicators */
.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-indicator.online {
    background: #28a745;
}

.status-indicator.offline {
    background: #dc3545;
}

.status-indicator.warning {
    background: #ffc107;
}

/* Loading spinner */
.loading-spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 1rem auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
    .main-header h1 {
        font-size: 2rem;
    }
    
    .main-header p {
        font-size: 1rem;
    }
    
    .chat-message {
        max-width: 95%;
    }
    
    .metric-card .metric-value {
        font-size: 1.5rem;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .chat-container {
        background: #2d3748;
        border-color: #4a5568;
    }
    
    .chat-message.assistant {
        background: #4a5568;
        color: white;
        border-color: #718096;
    }
    
    .metric-card {
        background: #2d3748;
        color: white;
    }
    
    .feature-card {
        background: #2d3748;
        color: white;
        border-color: #4a5568;
    }
    
    .chart-container {
        background: #2d3748;
    }
}

/* Animation classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Utility classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }

.p-1 { padding: 0.5rem; }
.p-2 { padding: 1rem; }
.p-3 { padding: 1.5rem; }

.rounded { border-radius: 8px; }
.rounded-lg { border-radius: 12px; }

.shadow-sm { box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); }
.shadow { box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); }
.shadow-lg { box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15); }
