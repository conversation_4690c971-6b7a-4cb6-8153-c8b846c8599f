"""
AI-Driven Data Explorer with PandasAI Integration
Main Streamlit Application
"""
import streamlit as st
import pandas as pd
import os
from dotenv import load_dotenv
import sys

# Add the current directory to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.pandasai_config import PandasAIConfig
from components.pandasai_chat import PandasAIChatInterface
from components.ai_integration import AIIntegrationManager

# Load environment variables
load_dotenv()

# Page configuration
st.set_page_config(
    page_title="AI-Driven Data Explorer",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded",
    menu_items={
        'Get Help': 'https://github.com/your-repo/ai-data-explorer',
        'Report a bug': 'https://github.com/your-repo/ai-data-explorer/issues',
        'About': "AI-Driven Data Explorer with PandasAI and Gemini 2.5 Flash"
    }
)

# Custom CSS for better UI
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        font-weight: bold;
        text-align: center;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-bottom: 2rem;
    }
    
    .feature-card {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin: 1rem 0;
        border-left: 4px solid #667eea;
    }
    
    .chat-container {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1rem;
        margin: 1rem 0;
    }
    
    .status-indicator {
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-right: 8px;
    }
    
    .status-connected {
        background-color: #28a745;
    }
    
    .status-disconnected {
        background-color: #dc3545;
    }
    
    .sidebar-section {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

def initialize_session_state():
    """Initialize session state variables"""
    if 'api_key' not in st.session_state:
        st.session_state.api_key = ""
    
    if 'current_dataframe' not in st.session_state:
        st.session_state.current_dataframe = None
    
    if 'ai_integration' not in st.session_state:
        st.session_state.ai_integration = None
    
    if 'chat_interface' not in st.session_state:
        st.session_state.chat_interface = None
    
    if 'dataset_uploaded' not in st.session_state:
        st.session_state.dataset_uploaded = False

def render_sidebar():
    """Render the sidebar with configuration and navigation"""
    
    with st.sidebar:
        st.markdown("## 🔧 Configuration")
        
        # API Key Configuration
        with st.container():
            st.markdown("### 🔑 API Settings")
            
            api_key = st.text_input(
                "Gemini API Key:",
                value=st.session_state.api_key,
                type="password",
                help="Enter your Google Gemini API key for AI functionality"
            )
            
            if api_key != st.session_state.api_key:
                st.session_state.api_key = api_key
                # Reinitialize AI components when API key changes
                if api_key:
                    st.session_state.ai_integration = AIIntegrationManager(api_key)
                    st.session_state.chat_interface = PandasAIChatInterface()
                    st.success("API key updated!")
                    st.rerun()
            
            # API Key Status
            if st.session_state.api_key:
                st.markdown('<span class="status-indicator status-connected"></span>API Key: Connected', 
                          unsafe_allow_html=True)
            else:
                st.markdown('<span class="status-indicator status-disconnected"></span>API Key: Not Set', 
                          unsafe_allow_html=True)
        
        st.divider()
        
        # Dataset Information
        with st.container():
            st.markdown("### 📊 Dataset Info")
            
            if st.session_state.current_dataframe is not None:
                df = st.session_state.current_dataframe
                st.markdown(f"**Shape:** {df.shape[0]} rows × {df.shape[1]} columns")
                st.markdown(f"**Memory:** {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
                st.markdown(f"**Missing Values:** {df.isnull().sum().sum()}")
                
                # Quick dataset preview
                with st.expander("📋 Quick Preview"):
                    st.dataframe(df.head(3), use_container_width=True)
            else:
                st.info("No dataset loaded")
        
        st.divider()
        
        # Navigation
        with st.container():
            st.markdown("### 🧭 Navigation")
            
            pages = {
                "🏠 Home": "home",
                "📤 Data Upload & Chat": "upload",
                "🔍 Conversational EDA": "eda", 
                "⚙️ Feature Engineering": "features",
                "📊 Visualizations": "viz",
                "📋 AI Reports": "reports",
                "📈 System Monitor": "monitor"
            }
            
            for page_name, page_key in pages.items():
                if st.button(page_name, key=f"nav_{page_key}", use_container_width=True):
                    st.session_state.current_page = page_key
                    st.rerun()
        
        st.divider()
        
        # Quick Actions
        with st.container():
            st.markdown("### ⚡ Quick Actions")
            
            if st.button("🗑️ Clear All Data", use_container_width=True):
                st.session_state.current_dataframe = None
                st.session_state.dataset_uploaded = False
                if st.session_state.chat_interface:
                    st.session_state.chat_interface.conversation_manager.clear_all_conversations()
                st.success("All data cleared!")
                st.rerun()
            
            if st.button("🔄 Reset Session", use_container_width=True):
                for key in list(st.session_state.keys()):
                    del st.session_state[key]
                st.success("Session reset!")
                st.rerun()

def render_home_page():
    """Render the home page"""
    
    st.markdown('<h1 class="main-header">🤖 AI-Driven Data Explorer</h1>', unsafe_allow_html=True)
    
    st.markdown("""
    <div style="text-align: center; font-size: 1.2rem; color: #666; margin-bottom: 3rem;">
        Powered by PandasAI & Gemini 2.5 Flash for Intelligent Data Analysis
    </div>
    """, unsafe_allow_html=True)
    
    # Feature highlights
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("""
        <div class="feature-card">
            <h3>🗣️ Conversational Analysis</h3>
            <p>Ask questions about your data in natural language and get instant insights with PandasAI.</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        st.markdown("""
        <div class="feature-card">
            <h3>🤖 Dual AI Power</h3>
            <p>Combine PandasAI's data expertise with Gemini 2.5 Flash's analytical intelligence.</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col3:
        st.markdown("""
        <div class="feature-card">
            <h3>⚙️ Smart Features</h3>
            <p>Automated feature engineering, intelligent visualizations, and comprehensive reporting.</p>
        </div>
        """, unsafe_allow_html=True)
    
    # Getting started section
    st.markdown("## 🚀 Getting Started")
    
    steps_col1, steps_col2 = st.columns(2)
    
    with steps_col1:
        st.markdown("""
        ### Step 1: Configure API
        1. Get your Gemini API key from Google AI Studio
        2. Enter it in the sidebar configuration
        3. Verify the connection status
        """)
        
        st.markdown("""
        ### Step 3: Start Exploring
        1. Use natural language to ask questions
        2. Generate visualizations through conversation
        3. Export insights and code for later use
        """)
    
    with steps_col2:
        st.markdown("""
        ### Step 2: Upload Data
        1. Navigate to "Data Upload & Chat"
        2. Upload CSV, Excel, JSON, or Parquet files
        3. Review the automatic data summary
        """)
        
        st.markdown("""
        ### Step 4: Advanced Analysis
        1. Use Feature Engineering for data enhancement
        2. Generate comprehensive reports
        3. Monitor system performance
        """)
    
    # Quick demo section
    if st.session_state.current_dataframe is not None and st.session_state.chat_interface:
        st.markdown("## 💬 Quick Chat Demo")
        
        with st.container():
            st.session_state.chat_interface.render_compact_chat(height=300)
    
    # Status overview
    st.markdown("## 📊 System Status")
    
    status_col1, status_col2, status_col3, status_col4 = st.columns(4)
    
    with status_col1:
        api_status = "✅ Connected" if st.session_state.api_key else "❌ Not Set"
        st.metric("API Status", api_status)
    
    with status_col2:
        data_status = "✅ Loaded" if st.session_state.current_dataframe is not None else "❌ No Data"
        st.metric("Dataset", data_status)
    
    with status_col3:
        chat_status = "✅ Ready" if st.session_state.chat_interface else "❌ Not Ready"
        st.metric("Chat Interface", chat_status)
    
    with status_col4:
        ai_status = "✅ Active" if st.session_state.ai_integration else "❌ Inactive"
        st.metric("AI Integration", ai_status)

def load_custom_css():
    """Load custom CSS styling"""
    try:
        css_path = os.path.join(os.path.dirname(__file__), "styles", "custom.css")
        if os.path.exists(css_path):
            with open(css_path, "r") as f:
                css = f.read()
            st.markdown(f"<style>{css}</style>", unsafe_allow_html=True)
    except Exception as e:
        # Silently fail if CSS file is not found
        pass

def main():
    """Main application function"""

    # Load custom CSS
    load_custom_css()

    # Initialize session state
    initialize_session_state()
    
    # Initialize current page if not set
    if 'current_page' not in st.session_state:
        st.session_state.current_page = 'home'
    
    # Render sidebar
    render_sidebar()
    
    # Render main content based on current page
    if st.session_state.current_page == 'home':
        render_home_page()
    elif st.session_state.current_page == 'upload':
        st.info("🚧 Data Upload & Chat page - Navigate to pages/01_Data_Upload_Chat.py")
    elif st.session_state.current_page == 'eda':
        st.info("🚧 Conversational EDA page - Navigate to pages/02_Conversational_EDA.py")
    elif st.session_state.current_page == 'features':
        st.info("🚧 Feature Engineering page - Navigate to pages/03_Feature_Engineering_AI.py")
    elif st.session_state.current_page == 'viz':
        st.info("🚧 Visualizations page - Navigate to pages/04_AI_Visualizations.py")
    elif st.session_state.current_page == 'reports':
        st.info("🚧 AI Reports page - Navigate to pages/05_AI_Reports.py")
    elif st.session_state.current_page == 'monitor':
        st.info("🚧 System Monitor page - Navigate to pages/06_System_Monitoring.py")
    
    # Footer
    st.markdown("---")
    st.markdown("""
    <div style="text-align: center; color: #666; font-size: 0.9rem;">
        AI-Driven Data Explorer | Powered by PandasAI & Gemini 2.5 Flash | 
        <a href="https://github.com/your-repo" target="_blank">GitHub</a> | 
        <a href="https://docs.your-app.com" target="_blank">Documentation</a>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
