"""
Interactive Visualizations with PandasAI Natural Language Chart Builder
"""
import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
import seaborn as sns
import matplotlib.pyplot as plt
from plotly.subplots import make_subplots
import os
import sys
from datetime import datetime

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from components.pandasai_chat import PandasAIChatInterface
from components.ai_integration import AIIntegrationManager
from config.pandasai_config import PandasAIConfig

# Page configuration
st.set_page_config(
    page_title="Interactive Visualizations - AI Data Explorer",
    page_icon="📊",
    layout="wide"
)

def initialize_session_state():
    """Initialize session state for Visualizations page"""
    if 'api_key' not in st.session_state:
        st.session_state.api_key = ""
    
    if 'current_dataframe' not in st.session_state:
        st.session_state.current_dataframe = None
    
    if 'viz_chat_interface' not in st.session_state:
        st.session_state.viz_chat_interface = None
    
    if 'generated_charts' not in st.session_state:
        st.session_state.generated_charts = []
    
    if 'chart_gallery' not in st.session_state:
        st.session_state.chart_gallery = []

def create_quick_visualization(dataframe, chart_type, x_col=None, y_col=None, color_col=None, size_col=None):
    """Create quick visualizations based on user selection"""
    
    try:
        if chart_type == "scatter":
            fig = px.scatter(dataframe, x=x_col, y=y_col, color=color_col, size=size_col,
                           title=f"Scatter Plot: {x_col} vs {y_col}")
        
        elif chart_type == "line":
            fig = px.line(dataframe, x=x_col, y=y_col, color=color_col,
                         title=f"Line Plot: {x_col} vs {y_col}")
        
        elif chart_type == "bar":
            if color_col:
                fig = px.bar(dataframe, x=x_col, y=y_col, color=color_col,
                           title=f"Bar Chart: {x_col} vs {y_col}")
            else:
                # Aggregate data for bar chart
                agg_data = dataframe.groupby(x_col)[y_col].mean().reset_index()
                fig = px.bar(agg_data, x=x_col, y=y_col,
                           title=f"Bar Chart: Average {y_col} by {x_col}")
        
        elif chart_type == "histogram":
            fig = px.histogram(dataframe, x=x_col, color=color_col, nbins=30,
                             title=f"Histogram: {x_col}")
        
        elif chart_type == "box":
            fig = px.box(dataframe, x=x_col, y=y_col, color=color_col,
                        title=f"Box Plot: {y_col} by {x_col}")
        
        elif chart_type == "violin":
            fig = px.violin(dataframe, x=x_col, y=y_col, color=color_col,
                          title=f"Violin Plot: {y_col} by {x_col}")
        
        elif chart_type == "heatmap":
            # Create correlation heatmap for numeric columns
            numeric_cols = dataframe.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) > 1:
                corr_matrix = dataframe[numeric_cols].corr()
                fig = px.imshow(corr_matrix, text_auto=True, aspect="auto",
                              title="Correlation Heatmap")
            else:
                return None
        
        elif chart_type == "pie":
            # Create pie chart for categorical data
            if x_col:
                value_counts = dataframe[x_col].value_counts()
                fig = px.pie(values=value_counts.values, names=value_counts.index,
                           title=f"Pie Chart: {x_col} Distribution")
            else:
                return None
        
        elif chart_type == "sunburst":
            # Create sunburst chart for hierarchical categorical data
            if x_col and color_col:
                fig = px.sunburst(dataframe, path=[x_col, color_col],
                                title=f"Sunburst Chart: {x_col} and {color_col}")
            else:
                return None
        
        else:
            return None
        
        # Update layout for better appearance
        fig.update_layout(
            height=500,
            showlegend=True,
            template="plotly_white"
        )
        
        return fig
        
    except Exception as e:
        st.error(f"Error creating visualization: {str(e)}")
        return None

def create_advanced_dashboard(dataframe):
    """Create an advanced dashboard with multiple visualizations"""
    
    try:
        # Create subplots
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=("Distribution Overview", "Correlation Analysis", 
                          "Categorical Analysis", "Time Series (if available)"),
            specs=[[{"secondary_y": False}, {"secondary_y": False}],
                   [{"secondary_y": False}, {"secondary_y": False}]]
        )
        
        numeric_cols = dataframe.select_dtypes(include=[np.number]).columns
        categorical_cols = dataframe.select_dtypes(include=['object', 'category']).columns
        datetime_cols = dataframe.select_dtypes(include=['datetime64']).columns
        
        # Plot 1: Distribution of first numeric column
        if len(numeric_cols) > 0:
            col = numeric_cols[0]
            hist_data = dataframe[col].dropna()
            fig.add_trace(
                go.Histogram(x=hist_data, name=f"{col} Distribution", nbinsx=30),
                row=1, col=1
            )
        
        # Plot 2: Correlation heatmap (simplified)
        if len(numeric_cols) > 1:
            corr_matrix = dataframe[numeric_cols[:5]].corr()  # Limit to first 5 columns
            fig.add_trace(
                go.Heatmap(z=corr_matrix.values, 
                          x=corr_matrix.columns, 
                          y=corr_matrix.columns,
                          colorscale='RdBu_r',
                          name="Correlation"),
                row=1, col=2
            )
        
        # Plot 3: Categorical distribution
        if len(categorical_cols) > 0:
            col = categorical_cols[0]
            value_counts = dataframe[col].value_counts().head(10)  # Top 10 categories
            fig.add_trace(
                go.Bar(x=value_counts.index, y=value_counts.values, 
                      name=f"{col} Distribution"),
                row=2, col=1
            )
        
        # Plot 4: Time series or scatter plot
        if len(datetime_cols) > 0 and len(numeric_cols) > 0:
            # Time series
            date_col = datetime_cols[0]
            num_col = numeric_cols[0]
            time_data = dataframe[[date_col, num_col]].dropna().sort_values(date_col)
            fig.add_trace(
                go.Scatter(x=time_data[date_col], y=time_data[num_col],
                          mode='lines', name=f"{num_col} over time"),
                row=2, col=2
            )
        elif len(numeric_cols) >= 2:
            # Scatter plot
            x_col, y_col = numeric_cols[0], numeric_cols[1]
            fig.add_trace(
                go.Scatter(x=dataframe[x_col], y=dataframe[y_col],
                          mode='markers', name=f"{x_col} vs {y_col}"),
                row=2, col=2
            )
        
        # Update layout
        fig.update_layout(
            height=800,
            title_text="Advanced Data Dashboard",
            showlegend=False,
            template="plotly_white"
        )
        
        return fig
        
    except Exception as e:
        st.error(f"Error creating dashboard: {str(e)}")
        return None

def render_natural_language_chart_builder():
    """Render the PandasAI natural language chart builder"""
    
    st.markdown("## 💬 Natural Language Chart Builder")
    
    if st.session_state.current_dataframe is None:
        st.warning("⚠️ Please upload a dataset first")
        return
    
    # Initialize chat interface if not already done
    if st.session_state.viz_chat_interface is None and st.session_state.api_key:
        st.session_state.viz_chat_interface = PandasAIChatInterface("viz_chat")
        st.session_state.viz_chat_interface.initialize_client(
            st.session_state.api_key, 
            st.session_state.current_dataframe
        )
    
    if st.session_state.viz_chat_interface:
        # Visualization-specific suggestions
        st.markdown("### 💡 Visualization Suggestions")
        
        df = st.session_state.current_dataframe
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        categorical_cols = df.select_dtypes(include=['object', 'category']).columns.tolist()
        datetime_cols = df.select_dtypes(include=['datetime64']).columns.tolist()
        
        viz_suggestions = []
        
        if len(numeric_cols) >= 2:
            viz_suggestions.extend([
                f"Create a scatter plot of {numeric_cols[0]} vs {numeric_cols[1]}",
                f"Show the correlation between {numeric_cols[0]} and {numeric_cols[1]}",
                f"Create a line chart showing the trend of {numeric_cols[0]}"
            ])
        
        if len(categorical_cols) > 0:
            viz_suggestions.extend([
                f"Create a bar chart showing the distribution of {categorical_cols[0]}",
                f"Show a pie chart for {categorical_cols[0]} categories"
            ])
        
        if len(categorical_cols) > 0 and len(numeric_cols) > 0:
            viz_suggestions.extend([
                f"Create a box plot of {numeric_cols[0]} by {categorical_cols[0]}",
                f"Show the average {numeric_cols[0]} for each {categorical_cols[0]}"
            ])
        
        if len(datetime_cols) > 0 and len(numeric_cols) > 0:
            viz_suggestions.append(f"Create a time series plot of {numeric_cols[0]} over {datetime_cols[0]}")
        
        viz_suggestions.extend([
            "Create a correlation heatmap of all numerical variables",
            "Show the distribution of all numerical columns",
            "Create an advanced dashboard with multiple visualizations"
        ])
        
        # Display suggestions in columns
        if viz_suggestions:
            suggestion_cols = st.columns(2)
            for i, suggestion in enumerate(viz_suggestions[:8]):
                col = suggestion_cols[i % 2]
                with col:
                    if st.button(suggestion, key=f"viz_suggestion_{i}", use_container_width=True):
                        st.session_state.viz_chat_interface._process_user_input(suggestion)
        
        # Render chat interface
        st.session_state.viz_chat_interface.render_chat_interface(height=600, show_suggestions=False)
    else:
        st.warning("⚠️ Please configure your API key to enable AI chart generation")

def render_quick_visualization_tools():
    """Render quick visualization creation tools"""
    
    st.markdown("## ⚡ Quick Visualization Tools")
    
    if st.session_state.current_dataframe is None:
        st.warning("⚠️ Please upload a dataset first")
        return
    
    df = st.session_state.current_dataframe
    numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
    categorical_cols = df.select_dtypes(include=['object', 'category']).columns.tolist()
    all_cols = df.columns.tolist()
    
    # Chart type selection
    chart_types = {
        "scatter": "Scatter Plot",
        "line": "Line Chart", 
        "bar": "Bar Chart",
        "histogram": "Histogram",
        "box": "Box Plot",
        "violin": "Violin Plot",
        "heatmap": "Correlation Heatmap",
        "pie": "Pie Chart",
        "sunburst": "Sunburst Chart"
    }
    
    col1, col2 = st.columns(2)
    
    with col1:
        selected_chart_type = st.selectbox("Select chart type:", 
                                         list(chart_types.keys()),
                                         format_func=lambda x: chart_types[x])
    
    with col2:
        if st.button("📊 Create Advanced Dashboard"):
            dashboard_fig = create_advanced_dashboard(df)
            if dashboard_fig:
                st.plotly_chart(dashboard_fig, use_container_width=True)
                
                # Add to gallery
                chart_info = {
                    "type": "Advanced Dashboard",
                    "timestamp": datetime.now(),
                    "figure": dashboard_fig,
                    "description": "Multi-panel dashboard with various visualizations"
                }
                st.session_state.chart_gallery.append(chart_info)
    
    # Chart configuration based on type
    x_col, y_col, color_col, size_col = None, None, None, None
    
    config_cols = st.columns(4)
    
    with config_cols[0]:
        if selected_chart_type in ["scatter", "line", "bar", "box", "violin"]:
            x_col = st.selectbox("X-axis:", all_cols, key="x_col")
        elif selected_chart_type in ["histogram", "pie"]:
            x_col = st.selectbox("Column:", all_cols, key="x_col_single")
    
    with config_cols[1]:
        if selected_chart_type in ["scatter", "line", "bar", "box", "violin"]:
            y_col = st.selectbox("Y-axis:", numeric_cols, key="y_col")
    
    with config_cols[2]:
        if selected_chart_type in ["scatter", "line", "bar", "histogram", "box", "violin"]:
            color_col = st.selectbox("Color by:", [None] + categorical_cols, key="color_col")
    
    with config_cols[3]:
        if selected_chart_type == "scatter":
            size_col = st.selectbox("Size by:", [None] + numeric_cols, key="size_col")
    
    # Create visualization button
    if st.button("🎨 Create Visualization", key="create_viz"):
        fig = create_quick_visualization(df, selected_chart_type, x_col, y_col, color_col, size_col)
        
        if fig:
            st.plotly_chart(fig, use_container_width=True)
            
            # Add to gallery
            chart_info = {
                "type": chart_types[selected_chart_type],
                "timestamp": datetime.now(),
                "figure": fig,
                "description": f"{chart_types[selected_chart_type]}: {x_col} vs {y_col}" if y_col else f"{chart_types[selected_chart_type]}: {x_col}"
            }
            st.session_state.chart_gallery.append(chart_info)
            
            st.success("✅ Chart created and added to gallery!")

def render_chart_gallery():
    """Render the chart gallery with saved visualizations"""
    
    st.markdown("## 🖼️ Chart Gallery")
    
    if not st.session_state.chart_gallery:
        st.info("📊 No charts in gallery yet. Create some visualizations to see them here!")
        return
    
    # Gallery controls
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("Total Charts", len(st.session_state.chart_gallery))
    
    with col2:
        if st.button("🗑️ Clear Gallery"):
            st.session_state.chart_gallery = []
            st.success("Gallery cleared!")
            st.rerun()
    
    with col3:
        if st.button("📥 Export All Charts"):
            st.info("Export functionality would save all charts as images or interactive HTML")
    
    # Display charts in gallery
    st.markdown("### Saved Charts")
    
    # Show charts in reverse chronological order (newest first)
    for i, chart_info in enumerate(reversed(st.session_state.chart_gallery)):
        with st.expander(f"📊 {chart_info['type']} - {chart_info['timestamp'].strftime('%H:%M:%S')}", expanded=False):
            
            col1, col2 = st.columns([3, 1])
            
            with col1:
                st.plotly_chart(chart_info['figure'], use_container_width=True)
            
            with col2:
                st.markdown("**Chart Info:**")
                st.write(f"**Type:** {chart_info['type']}")
                st.write(f"**Created:** {chart_info['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}")
                st.write(f"**Description:** {chart_info['description']}")
                
                if st.button("🗑️ Remove", key=f"remove_chart_{i}"):
                    # Remove chart from gallery
                    chart_index = len(st.session_state.chart_gallery) - 1 - i
                    st.session_state.chart_gallery.pop(chart_index)
                    st.success("Chart removed!")
                    st.rerun()

def render_visualization_insights():
    """Render insights about the visualizations"""
    
    st.markdown("## 🔍 Visualization Insights")
    
    if st.session_state.current_dataframe is None:
        st.info("📊 Load a dataset to see visualization insights")
        return
    
    df = st.session_state.current_dataframe
    
    # Data overview for visualization recommendations
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    categorical_cols = df.select_dtypes(include=['object', 'category']).columns
    datetime_cols = df.select_dtypes(include=['datetime64']).columns
    
    insights = []
    
    # Recommend visualizations based on data types
    if len(numeric_cols) >= 2:
        insights.append("💡 **Correlation Analysis**: With multiple numeric columns, consider creating correlation heatmaps and scatter plots")
    
    if len(categorical_cols) > 0:
        insights.append("💡 **Category Distribution**: Use bar charts or pie charts to explore categorical variable distributions")
    
    if len(datetime_cols) > 0:
        insights.append("💡 **Time Series Analysis**: Create time series plots to identify trends and patterns over time")
    
    if len(numeric_cols) > 0 and len(categorical_cols) > 0:
        insights.append("💡 **Group Comparisons**: Use box plots or violin plots to compare numeric distributions across categories")
    
    # Data quality insights for visualization
    missing_data = df.isnull().sum().sum()
    if missing_data > 0:
        insights.append(f"⚠️ **Missing Data**: {missing_data} missing values detected. Consider handling missing data before visualization")
    
    # Outlier detection insights
    for col in numeric_cols[:3]:  # Check first 3 numeric columns
        Q1 = df[col].quantile(0.25)
        Q3 = df[col].quantile(0.75)
        IQR = Q3 - Q1
        outliers = df[(df[col] < Q1 - 1.5 * IQR) | (df[col] > Q3 + 1.5 * IQR)][col]
        
        if len(outliers) > 0:
            insights.append(f"📊 **Outliers in {col}**: {len(outliers)} potential outliers detected. Box plots can help visualize these")
    
    # Display insights
    if insights:
        for insight in insights:
            st.markdown(insight)
    else:
        st.info("No specific visualization insights available for this dataset")
    
    # Visualization best practices
    st.markdown("### 📋 Visualization Best Practices")
    
    best_practices = [
        "🎯 **Choose the right chart type** for your data and message",
        "🎨 **Use color effectively** to highlight important information",
        "📏 **Keep it simple** - avoid cluttered visualizations",
        "📊 **Label your axes** and provide clear titles",
        "🔍 **Consider your audience** when designing visualizations",
        "📱 **Make it interactive** when possible for better exploration"
    ]
    
    for practice in best_practices:
        st.markdown(practice)

def main():
    """Main function for Interactive Visualizations page"""
    
    # Initialize session state
    initialize_session_state()
    
    # Page header
    st.markdown("# 📊 Interactive Visualizations with PandasAI")
    st.markdown("Create stunning visualizations through natural language and interactive tools!")
    
    # Check if data is loaded
    if st.session_state.current_dataframe is None:
        st.warning("⚠️ Please upload a dataset first using the Data Upload page")
        if st.button("🔙 Go to Data Upload"):
            st.switch_page("pages/01_Data_Upload_Chat.py")
        return
    
    # Main layout tabs
    chat_tab, tools_tab, gallery_tab, insights_tab = st.tabs([
        "💬 AI Chart Builder", "⚡ Quick Tools", "🖼️ Gallery", "🔍 Insights"
    ])
    
    with chat_tab:
        render_natural_language_chart_builder()
    
    with tools_tab:
        render_quick_visualization_tools()
    
    with gallery_tab:
        render_chart_gallery()
    
    with insights_tab:
        render_visualization_insights()
    
    # Dataset info sidebar
    with st.sidebar:
        st.markdown("## 📊 Dataset Info")
        
        df = st.session_state.current_dataframe
        
        st.metric("Rows", f"{df.shape[0]:,}")
        st.metric("Columns", df.shape[1])
        
        # Column types
        numeric_count = len(df.select_dtypes(include=[np.number]).columns)
        categorical_count = len(df.select_dtypes(include=['object', 'category']).columns)
        datetime_count = len(df.select_dtypes(include=['datetime64']).columns)
        
        st.markdown("**Column Types:**")
        st.write(f"📊 Numeric: {numeric_count}")
        st.write(f"📝 Categorical: {categorical_count}")
        st.write(f"📅 DateTime: {datetime_count}")
        
        # Chart gallery info
        if st.session_state.chart_gallery:
            st.markdown("## 🖼️ Gallery Stats")
            st.metric("Saved Charts", len(st.session_state.chart_gallery))
            
            # Chart type distribution
            chart_types = {}
            for chart in st.session_state.chart_gallery:
                chart_type = chart['type']
                chart_types[chart_type] = chart_types.get(chart_type, 0) + 1
            
            st.markdown("**Chart Types:**")
            for chart_type, count in chart_types.items():
                st.write(f"• {chart_type}: {count}")

if __name__ == "__main__":
    main()
