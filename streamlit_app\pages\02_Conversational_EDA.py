"""
Conversational EDA with PandasAI + Automated Results Page
"""
import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
import seaborn as sns
import matplotlib.pyplot as plt
import os
import sys
from io import BytesIO
import base64

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from components.pandasai_chat import PandasAIChatInterface
from components.ai_integration import AIIntegrationManager
from config.pandasai_config import PandasAIConfig

# Page configuration
st.set_page_config(
    page_title="Conversational EDA - AI Data Explorer",
    page_icon="🔍",
    layout="wide"
)

def initialize_session_state():
    """Initialize session state for EDA page"""
    if 'api_key' not in st.session_state:
        st.session_state.api_key = ""
    
    if 'current_dataframe' not in st.session_state:
        st.session_state.current_dataframe = None
    
    if 'eda_chat_interface' not in st.session_state:
        st.session_state.eda_chat_interface = None
    
    if 'eda_insights' not in st.session_state:
        st.session_state.eda_insights = {}
    
    if 'automated_eda_complete' not in st.session_state:
        st.session_state.automated_eda_complete = False

def generate_automated_eda_insights(dataframe):
    """Generate automated EDA insights using multiple methods"""
    
    insights = {
        "basic_stats": {},
        "data_quality": {},
        "correlations": {},
        "distributions": {},
        "outliers": {},
        "patterns": {}
    }
    
    try:
        # Basic statistics
        insights["basic_stats"] = {
            "shape": dataframe.shape,
            "memory_usage": dataframe.memory_usage(deep=True).sum() / (1024**2),
            "numeric_columns": len(dataframe.select_dtypes(include=[np.number]).columns),
            "categorical_columns": len(dataframe.select_dtypes(include=['object', 'category']).columns),
            "datetime_columns": len(dataframe.select_dtypes(include=['datetime64']).columns)
        }
        
        # Data quality assessment
        insights["data_quality"] = {
            "missing_values": dataframe.isnull().sum().to_dict(),
            "duplicate_rows": dataframe.duplicated().sum(),
            "completeness_score": (1 - dataframe.isnull().sum().sum() / (dataframe.shape[0] * dataframe.shape[1])) * 100
        }
        
        # Correlation analysis for numeric columns
        numeric_cols = dataframe.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) > 1:
            corr_matrix = dataframe[numeric_cols].corr()
            
            # Find high correlations
            high_corr_pairs = []
            for i in range(len(corr_matrix.columns)):
                for j in range(i+1, len(corr_matrix.columns)):
                    corr_val = corr_matrix.iloc[i, j]
                    if abs(corr_val) > 0.7:  # High correlation threshold
                        high_corr_pairs.append({
                            "col1": corr_matrix.columns[i],
                            "col2": corr_matrix.columns[j],
                            "correlation": corr_val
                        })
            
            insights["correlations"] = {
                "correlation_matrix": corr_matrix.to_dict(),
                "high_correlations": high_corr_pairs
            }
        
        # Distribution analysis
        distribution_insights = {}
        for col in numeric_cols:
            col_data = dataframe[col].dropna()
            if len(col_data) > 0:
                distribution_insights[col] = {
                    "mean": col_data.mean(),
                    "median": col_data.median(),
                    "std": col_data.std(),
                    "skewness": col_data.skew(),
                    "kurtosis": col_data.kurtosis()
                }
        
        insights["distributions"] = distribution_insights
        
        # Outlier detection using IQR method
        outlier_insights = {}
        for col in numeric_cols:
            col_data = dataframe[col].dropna()
            if len(col_data) > 0:
                Q1 = col_data.quantile(0.25)
                Q3 = col_data.quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                outliers = col_data[(col_data < lower_bound) | (col_data > upper_bound)]
                outlier_insights[col] = {
                    "outlier_count": len(outliers),
                    "outlier_percentage": (len(outliers) / len(col_data)) * 100,
                    "lower_bound": lower_bound,
                    "upper_bound": upper_bound
                }
        
        insights["outliers"] = outlier_insights
        
        # Pattern detection
        patterns = []
        
        # Check for potential time series data
        datetime_cols = dataframe.select_dtypes(include=['datetime64']).columns
        if len(datetime_cols) > 0:
            patterns.append("Time series data detected")
        
        # Check for categorical imbalances
        categorical_cols = dataframe.select_dtypes(include=['object', 'category']).columns
        for col in categorical_cols:
            value_counts = dataframe[col].value_counts()
            if len(value_counts) > 1:
                imbalance_ratio = value_counts.iloc[0] / value_counts.iloc[-1]
                if imbalance_ratio > 10:
                    patterns.append(f"High class imbalance in {col}")
        
        # Check for potential ID columns
        for col in dataframe.columns:
            if dataframe[col].nunique() == len(dataframe):
                patterns.append(f"Potential ID column: {col}")
        
        insights["patterns"] = patterns
        
    except Exception as e:
        st.error(f"Error generating automated insights: {str(e)}")
    
    return insights

def render_automated_eda_panel():
    """Render the automated EDA results panel"""
    
    if st.session_state.current_dataframe is None:
        st.info("📊 Load a dataset to see automated EDA results")
        return
    
    df = st.session_state.current_dataframe
    
    # Generate insights if not already done
    if not st.session_state.automated_eda_complete:
        with st.spinner("🔍 Generating automated EDA insights..."):
            st.session_state.eda_insights = generate_automated_eda_insights(df)
            st.session_state.automated_eda_complete = True
    
    insights = st.session_state.eda_insights
    
    # EDA Results Tabs
    basic_tab, quality_tab, corr_tab, dist_tab, outlier_tab = st.tabs([
        "📊 Basic Stats", "🔍 Data Quality", "🔗 Correlations", "📈 Distributions", "⚠️ Outliers"
    ])
    
    with basic_tab:
        render_basic_stats(insights.get("basic_stats", {}))
    
    with quality_tab:
        render_data_quality(insights.get("data_quality", {}), df)
    
    with corr_tab:
        render_correlations(insights.get("correlations", {}), df)
    
    with dist_tab:
        render_distributions(insights.get("distributions", {}), df)
    
    with outlier_tab:
        render_outliers(insights.get("outliers", {}), df)

def render_basic_stats(basic_stats):
    """Render basic statistics"""
    
    if not basic_stats:
        st.info("No basic statistics available")
        return
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total Rows", f"{basic_stats.get('shape', [0, 0])[0]:,}")
    
    with col2:
        st.metric("Total Columns", basic_stats.get('shape', [0, 0])[1])
    
    with col3:
        st.metric("Memory Usage", f"{basic_stats.get('memory_usage', 0):.2f} MB")
    
    with col4:
        completeness = st.session_state.eda_insights.get("data_quality", {}).get("completeness_score", 0)
        st.metric("Data Completeness", f"{completeness:.1f}%")
    
    # Column type breakdown
    st.markdown("### Column Type Distribution")
    
    col_types = {
        "Numeric": basic_stats.get('numeric_columns', 0),
        "Categorical": basic_stats.get('categorical_columns', 0),
        "DateTime": basic_stats.get('datetime_columns', 0)
    }
    
    if sum(col_types.values()) > 0:
        fig = px.pie(
            values=list(col_types.values()),
            names=list(col_types.keys()),
            title="Column Types Distribution"
        )
        st.plotly_chart(fig, use_container_width=True)

def render_data_quality(quality_data, dataframe):
    """Render data quality assessment"""
    
    if not quality_data:
        st.info("No data quality information available")
        return
    
    # Missing values analysis
    st.markdown("### Missing Values Analysis")
    
    missing_values = quality_data.get("missing_values", {})
    if missing_values:
        missing_df = pd.DataFrame([
            {"Column": col, "Missing Count": count, "Missing %": (count/len(dataframe))*100}
            for col, count in missing_values.items() if count > 0
        ])
        
        if not missing_df.empty:
            st.dataframe(missing_df, use_container_width=True)
            
            # Missing values visualization
            fig = px.bar(
                missing_df,
                x="Column",
                y="Missing %",
                title="Missing Values by Column (%)"
            )
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.success("✅ No missing values found!")
    
    # Duplicate rows
    duplicate_count = quality_data.get("duplicate_rows", 0)
    if duplicate_count > 0:
        st.warning(f"⚠️ Found {duplicate_count} duplicate rows ({(duplicate_count/len(dataframe))*100:.2f}%)")
    else:
        st.success("✅ No duplicate rows found!")

def render_correlations(corr_data, dataframe):
    """Render correlation analysis"""
    
    if not corr_data:
        st.info("No correlation data available (requires numeric columns)")
        return
    
    # Correlation matrix heatmap
    st.markdown("### Correlation Matrix")
    
    corr_matrix = corr_data.get("correlation_matrix", {})
    if corr_matrix:
        corr_df = pd.DataFrame(corr_matrix)
        
        fig = px.imshow(
            corr_df,
            text_auto=True,
            aspect="auto",
            title="Correlation Heatmap",
            color_continuous_scale="RdBu_r"
        )
        st.plotly_chart(fig, use_container_width=True)
    
    # High correlations
    high_corr = corr_data.get("high_correlations", [])
    if high_corr:
        st.markdown("### High Correlations (|r| > 0.7)")
        
        high_corr_df = pd.DataFrame(high_corr)
        high_corr_df["Correlation"] = high_corr_df["correlation"].round(3)
        st.dataframe(high_corr_df[["col1", "col2", "Correlation"]], use_container_width=True)
    else:
        st.info("No high correlations found")

def render_distributions(dist_data, dataframe):
    """Render distribution analysis"""
    
    if not dist_data:
        st.info("No distribution data available")
        return
    
    st.markdown("### Distribution Analysis")
    
    # Select column for detailed distribution
    numeric_cols = list(dist_data.keys())
    if numeric_cols:
        selected_col = st.selectbox("Select column for distribution analysis:", numeric_cols)
        
        if selected_col:
            col_stats = dist_data[selected_col]
            
            # Distribution metrics
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric("Mean", f"{col_stats.get('mean', 0):.2f}")
            
            with col2:
                st.metric("Median", f"{col_stats.get('median', 0):.2f}")
            
            with col3:
                st.metric("Std Dev", f"{col_stats.get('std', 0):.2f}")
            
            with col4:
                st.metric("Skewness", f"{col_stats.get('skewness', 0):.2f}")
            
            # Distribution plot
            fig = px.histogram(
                dataframe,
                x=selected_col,
                title=f"Distribution of {selected_col}",
                marginal="box"
            )
            st.plotly_chart(fig, use_container_width=True)

def render_outliers(outlier_data, dataframe):
    """Render outlier analysis"""
    
    if not outlier_data:
        st.info("No outlier data available")
        return
    
    st.markdown("### Outlier Analysis")
    
    # Outlier summary
    outlier_summary = []
    for col, data in outlier_data.items():
        outlier_summary.append({
            "Column": col,
            "Outlier Count": data.get("outlier_count", 0),
            "Outlier %": f"{data.get('outlier_percentage', 0):.2f}%"
        })
    
    if outlier_summary:
        outlier_df = pd.DataFrame(outlier_summary)
        st.dataframe(outlier_df, use_container_width=True)
        
        # Outlier visualization
        cols_with_outliers = [item["Column"] for item in outlier_summary if item["Outlier Count"] > 0]
        
        if cols_with_outliers:
            selected_outlier_col = st.selectbox("Select column for outlier visualization:", cols_with_outliers)
            
            if selected_outlier_col:
                fig = px.box(
                    dataframe,
                    y=selected_outlier_col,
                    title=f"Box Plot - {selected_outlier_col} (Outliers Highlighted)"
                )
                st.plotly_chart(fig, use_container_width=True)
        else:
            st.success("✅ No significant outliers detected!")

def main():
    """Main function for Conversational EDA page"""
    
    # Initialize session state
    initialize_session_state()
    
    # Page header
    st.markdown("# 🔍 Conversational EDA with PandasAI + Automated Results")
    st.markdown("Explore your data through natural language conversation and automated analysis!")
    
    # Check if data is loaded
    if st.session_state.current_dataframe is None:
        st.warning("⚠️ Please upload a dataset first using the Data Upload page")
        if st.button("🔙 Go to Data Upload"):
            st.switch_page("pages/01_Data_Upload_Chat.py")
        return
    
    # Initialize chat interface if not already done
    if st.session_state.eda_chat_interface is None and st.session_state.api_key:
        st.session_state.eda_chat_interface = PandasAIChatInterface("eda_chat")
        st.session_state.eda_chat_interface.initialize_client(
            st.session_state.api_key, 
            st.session_state.current_dataframe
        )
    
    # Main layout: Chat on left, Automated results on right
    chat_col, results_col = st.columns([1, 1])
    
    with chat_col:
        st.markdown("## 💬 PandasAI Conversational Explorer")
        
        if st.session_state.eda_chat_interface:
            # EDA-specific suggestions
            st.markdown("### 💡 EDA Question Suggestions")
            
            eda_suggestions = [
                "What are the main characteristics of this dataset?",
                "Show me the correlation between numerical variables",
                "Which columns have missing values?",
                "What are the distributions of the numerical columns?",
                "Are there any outliers in the data?",
                "Show me summary statistics for all columns"
            ]
            
            suggestion_cols = st.columns(2)
            for i, suggestion in enumerate(eda_suggestions):
                col = suggestion_cols[i % 2]
                with col:
                    if st.button(suggestion, key=f"eda_suggestion_{i}", use_container_width=True):
                        # Process the suggestion through chat
                        st.session_state.eda_chat_interface._process_user_input(suggestion)
            
            # Render chat interface
            st.session_state.eda_chat_interface.render_chat_interface(height=600, show_suggestions=False)
        else:
            st.warning("⚠️ Please configure your API key to enable conversational EDA")
    
    with results_col:
        st.markdown("## 📊 Automated EDA Results")
        render_automated_eda_panel()

if __name__ == "__main__":
    main()
