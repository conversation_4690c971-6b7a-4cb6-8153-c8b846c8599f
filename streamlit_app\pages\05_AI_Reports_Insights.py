"""
AI Reports & Insights with PandasAI + Gemini 2.5 Integration
"""
import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import json
import os
import sys
from io import StringIO, BytesIO
import base64

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from components.pandasai_chat import PandasAIChatInterface
from components.ai_integration import AIIntegrationManager
from config.pandasai_config import PandasAIConfig

# Page configuration
st.set_page_config(
    page_title="AI Reports & Insights - AI Data Explorer",
    page_icon="📋",
    layout="wide"
)

def initialize_session_state():
    """Initialize session state for Reports page"""
    if 'api_key' not in st.session_state:
        st.session_state.api_key = ""
    
    if 'current_dataframe' not in st.session_state:
        st.session_state.current_dataframe = None
    
    if 'reports_chat_interface' not in st.session_state:
        st.session_state.reports_chat_interface = None
    
    if 'generated_reports' not in st.session_state:
        st.session_state.generated_reports = []
    
    if 'ai_insights' not in st.session_state:
        st.session_state.ai_insights = {}
    
    if 'report_templates' not in st.session_state:
        st.session_state.report_templates = {}

def generate_comprehensive_insights(dataframe):
    """Generate comprehensive AI insights about the dataset"""
    
    insights = {
        "data_overview": {},
        "statistical_insights": {},
        "patterns_detected": [],
        "recommendations": [],
        "quality_assessment": {},
        "business_insights": []
    }
    
    try:
        # Data Overview
        insights["data_overview"] = {
            "total_records": len(dataframe),
            "total_features": len(dataframe.columns),
            "numeric_features": len(dataframe.select_dtypes(include=[np.number]).columns),
            "categorical_features": len(dataframe.select_dtypes(include=['object', 'category']).columns),
            "datetime_features": len(dataframe.select_dtypes(include=['datetime64']).columns),
            "memory_usage_mb": dataframe.memory_usage(deep=True).sum() / (1024**2)
        }
        
        # Statistical Insights
        numeric_cols = dataframe.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) > 0:
            insights["statistical_insights"] = {
                "high_variance_features": [],
                "skewed_distributions": [],
                "potential_outliers": {},
                "correlation_insights": []
            }
            
            # High variance features
            for col in numeric_cols:
                cv = dataframe[col].std() / dataframe[col].mean() if dataframe[col].mean() != 0 else 0
                if cv > 1:  # Coefficient of variation > 1
                    insights["statistical_insights"]["high_variance_features"].append({
                        "feature": col,
                        "coefficient_of_variation": cv
                    })
            
            # Skewed distributions
            for col in numeric_cols:
                skewness = dataframe[col].skew()
                if abs(skewness) > 1:  # Highly skewed
                    insights["statistical_insights"]["skewed_distributions"].append({
                        "feature": col,
                        "skewness": skewness,
                        "direction": "right" if skewness > 0 else "left"
                    })
            
            # Correlation insights
            if len(numeric_cols) > 1:
                corr_matrix = dataframe[numeric_cols].corr()
                high_corr_pairs = []
                
                for i in range(len(corr_matrix.columns)):
                    for j in range(i+1, len(corr_matrix.columns)):
                        corr_val = corr_matrix.iloc[i, j]
                        if abs(corr_val) > 0.7:
                            high_corr_pairs.append({
                                "feature1": corr_matrix.columns[i],
                                "feature2": corr_matrix.columns[j],
                                "correlation": corr_val,
                                "strength": "strong" if abs(corr_val) > 0.8 else "moderate"
                            })
                
                insights["statistical_insights"]["correlation_insights"] = high_corr_pairs
        
        # Pattern Detection
        patterns = []
        
        # Check for time series patterns
        datetime_cols = dataframe.select_dtypes(include=['datetime64']).columns
        if len(datetime_cols) > 0:
            patterns.append("Time series data detected - temporal analysis recommended")
        
        # Check for categorical imbalances
        categorical_cols = dataframe.select_dtypes(include=['object', 'category']).columns
        for col in categorical_cols:
            value_counts = dataframe[col].value_counts()
            if len(value_counts) > 1:
                imbalance_ratio = value_counts.iloc[0] / value_counts.iloc[-1]
                if imbalance_ratio > 10:
                    patterns.append(f"High class imbalance detected in {col}")
        
        # Check for potential ID columns
        for col in dataframe.columns:
            if dataframe[col].nunique() == len(dataframe):
                patterns.append(f"Unique identifier column detected: {col}")
        
        insights["patterns_detected"] = patterns
        
        # Quality Assessment
        missing_data = dataframe.isnull().sum()
        duplicate_rows = dataframe.duplicated().sum()
        
        insights["quality_assessment"] = {
            "missing_data_percentage": (missing_data.sum() / (len(dataframe) * len(dataframe.columns))) * 100,
            "duplicate_rows": duplicate_rows,
            "duplicate_percentage": (duplicate_rows / len(dataframe)) * 100,
            "completeness_score": 100 - (missing_data.sum() / (len(dataframe) * len(dataframe.columns))) * 100
        }
        
        # Recommendations
        recommendations = []
        
        if insights["quality_assessment"]["missing_data_percentage"] > 5:
            recommendations.append("Consider data imputation strategies for missing values")
        
        if insights["quality_assessment"]["duplicate_percentage"] > 1:
            recommendations.append("Remove duplicate rows to improve data quality")
        
        if len(insights["statistical_insights"].get("high_variance_features", [])) > 0:
            recommendations.append("Consider feature scaling for high variance features")
        
        if len(insights["statistical_insights"].get("skewed_distributions", [])) > 0:
            recommendations.append("Apply transformations to normalize skewed distributions")
        
        insights["recommendations"] = recommendations
        
        # Business Insights (placeholder for AI-generated insights)
        business_insights = [
            "Dataset appears suitable for predictive modeling",
            "Strong correlations suggest potential feature engineering opportunities",
            "Data quality is sufficient for analysis with minor preprocessing"
        ]
        
        insights["business_insights"] = business_insights
        
    except Exception as e:
        st.error(f"Error generating insights: {str(e)}")
    
    return insights

def create_executive_summary(dataframe, insights):
    """Create an executive summary of the dataset"""
    
    summary = {
        "title": "Data Analysis Executive Summary",
        "generated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "key_findings": [],
        "data_characteristics": {},
        "recommendations": [],
        "next_steps": []
    }
    
    try:
        # Key findings
        overview = insights.get("data_overview", {})
        quality = insights.get("quality_assessment", {})
        
        summary["key_findings"] = [
            f"Dataset contains {overview.get('total_records', 0):,} records with {overview.get('total_features', 0)} features",
            f"Data completeness score: {quality.get('completeness_score', 0):.1f}%",
            f"Memory footprint: {overview.get('memory_usage_mb', 0):.2f} MB",
            f"Feature composition: {overview.get('numeric_features', 0)} numeric, {overview.get('categorical_features', 0)} categorical"
        ]
        
        # Data characteristics
        summary["data_characteristics"] = {
            "size": "Large" if overview.get('total_records', 0) > 10000 else "Medium" if overview.get('total_records', 0) > 1000 else "Small",
            "complexity": "High" if overview.get('total_features', 0) > 20 else "Medium" if overview.get('total_features', 0) > 10 else "Low",
            "quality": "High" if quality.get('completeness_score', 0) > 95 else "Medium" if quality.get('completeness_score', 0) > 80 else "Low"
        }
        
        # Recommendations
        summary["recommendations"] = insights.get("recommendations", [])
        
        # Next steps
        summary["next_steps"] = [
            "Perform detailed exploratory data analysis",
            "Implement data preprocessing pipeline",
            "Develop predictive models if applicable",
            "Create comprehensive visualizations"
        ]
        
    except Exception as e:
        st.error(f"Error creating executive summary: {str(e)}")
    
    return summary

def render_conversational_report_builder():
    """Render the conversational report builder interface"""
    
    st.markdown("## 💬 Conversational Report Builder")
    
    if st.session_state.current_dataframe is None:
        st.warning("⚠️ Please upload a dataset first")
        return
    
    # Initialize chat interface if not already done
    if st.session_state.reports_chat_interface is None and st.session_state.api_key:
        st.session_state.reports_chat_interface = PandasAIChatInterface("reports_chat")
        st.session_state.reports_chat_interface.initialize_client(
            st.session_state.api_key, 
            st.session_state.current_dataframe
        )
    
    if st.session_state.reports_chat_interface:
        # Report-specific suggestions
        st.markdown("### 💡 Report Generation Suggestions")
        
        report_suggestions = [
            "Generate a comprehensive data quality report",
            "Create an executive summary of the dataset",
            "Analyze the key patterns and trends in the data",
            "Provide recommendations for data preprocessing",
            "Generate insights about feature relationships",
            "Create a statistical summary report",
            "Identify potential data issues and anomalies",
            "Suggest next steps for analysis"
        ]
        
        suggestion_cols = st.columns(2)
        for i, suggestion in enumerate(report_suggestions):
            col = suggestion_cols[i % 2]
            with col:
                if st.button(suggestion, key=f"report_suggestion_{i}", use_container_width=True):
                    st.session_state.reports_chat_interface._process_user_input(suggestion)
        
        # Render chat interface
        st.session_state.reports_chat_interface.render_chat_interface(height=600, show_suggestions=False)
    else:
        st.warning("⚠️ Please configure your API key to enable AI report generation")

def render_automated_insights_panel():
    """Render the automated insights panel"""
    
    st.markdown("## 🤖 Automated AI Insights")
    
    if st.session_state.current_dataframe is None:
        st.info("📊 Load a dataset to see automated insights")
        return
    
    df = st.session_state.current_dataframe
    
    # Generate insights if not already done
    if not st.session_state.ai_insights:
        with st.spinner("🔍 Generating AI insights..."):
            st.session_state.ai_insights = generate_comprehensive_insights(df)
    
    insights = st.session_state.ai_insights
    
    # Insights tabs
    overview_tab, patterns_tab, quality_tab, recommendations_tab = st.tabs([
        "📊 Overview", "🔍 Patterns", "✅ Quality", "💡 Recommendations"
    ])
    
    with overview_tab:
        render_overview_insights(insights.get("data_overview", {}))
    
    with patterns_tab:
        render_pattern_insights(insights.get("patterns_detected", []), insights.get("statistical_insights", {}))
    
    with quality_tab:
        render_quality_insights(insights.get("quality_assessment", {}))
    
    with recommendations_tab:
        render_recommendation_insights(insights.get("recommendations", []), insights.get("business_insights", []))

def render_overview_insights(overview_data):
    """Render overview insights"""
    
    if not overview_data:
        st.info("No overview data available")
        return
    
    # Metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total Records", f"{overview_data.get('total_records', 0):,}")
    
    with col2:
        st.metric("Total Features", overview_data.get('total_features', 0))
    
    with col3:
        st.metric("Memory Usage", f"{overview_data.get('memory_usage_mb', 0):.2f} MB")
    
    with col4:
        numeric_ratio = overview_data.get('numeric_features', 0) / max(overview_data.get('total_features', 1), 1)
        st.metric("Numeric Ratio", f"{numeric_ratio:.1%}")
    
    # Feature composition chart
    st.markdown("### Feature Type Distribution")
    
    feature_types = {
        "Numeric": overview_data.get('numeric_features', 0),
        "Categorical": overview_data.get('categorical_features', 0),
        "DateTime": overview_data.get('datetime_features', 0)
    }
    
    if sum(feature_types.values()) > 0:
        fig = px.pie(
            values=list(feature_types.values()),
            names=list(feature_types.keys()),
            title="Feature Type Composition"
        )
        st.plotly_chart(fig, use_container_width=True)

def render_pattern_insights(patterns, statistical_insights):
    """Render pattern detection insights"""
    
    st.markdown("### Detected Patterns")
    
    if patterns:
        for pattern in patterns:
            st.info(f"🔍 {pattern}")
    else:
        st.info("No specific patterns detected")
    
    # Statistical insights
    if statistical_insights:
        st.markdown("### Statistical Insights")
        
        # High variance features
        high_var = statistical_insights.get("high_variance_features", [])
        if high_var:
            st.markdown("**High Variance Features:**")
            for feature_info in high_var:
                st.write(f"• {feature_info['feature']}: CV = {feature_info['coefficient_of_variation']:.2f}")
        
        # Skewed distributions
        skewed = statistical_insights.get("skewed_distributions", [])
        if skewed:
            st.markdown("**Skewed Distributions:**")
            for dist_info in skewed:
                st.write(f"• {dist_info['feature']}: {dist_info['direction']} skew ({dist_info['skewness']:.2f})")
        
        # Correlation insights
        correlations = statistical_insights.get("correlation_insights", [])
        if correlations:
            st.markdown("**Strong Correlations:**")
            for corr_info in correlations:
                st.write(f"• {corr_info['feature1']} ↔ {corr_info['feature2']}: {corr_info['correlation']:.3f} ({corr_info['strength']})")

def render_quality_insights(quality_data):
    """Render data quality insights"""
    
    if not quality_data:
        st.info("No quality assessment data available")
        return
    
    # Quality metrics
    col1, col2, col3 = st.columns(3)
    
    with col1:
        completeness = quality_data.get('completeness_score', 0)
        st.metric("Completeness Score", f"{completeness:.1f}%")
    
    with col2:
        missing_pct = quality_data.get('missing_data_percentage', 0)
        st.metric("Missing Data", f"{missing_pct:.1f}%")
    
    with col3:
        duplicate_pct = quality_data.get('duplicate_percentage', 0)
        st.metric("Duplicate Rows", f"{duplicate_pct:.1f}%")
    
    # Quality assessment
    st.markdown("### Quality Assessment")
    
    if completeness >= 95:
        st.success("✅ Excellent data completeness")
    elif completeness >= 80:
        st.warning("⚠️ Good data completeness with minor gaps")
    else:
        st.error("❌ Poor data completeness - significant preprocessing needed")
    
    if duplicate_pct > 5:
        st.warning(f"⚠️ High duplicate rate: {quality_data.get('duplicate_rows', 0)} duplicate rows found")
    elif duplicate_pct > 0:
        st.info(f"ℹ️ Minor duplicates: {quality_data.get('duplicate_rows', 0)} duplicate rows found")
    else:
        st.success("✅ No duplicate rows detected")

def render_recommendation_insights(recommendations, business_insights):
    """Render recommendations and business insights"""
    
    st.markdown("### Technical Recommendations")
    
    if recommendations:
        for rec in recommendations:
            st.info(f"💡 {rec}")
    else:
        st.success("✅ No specific technical recommendations - data appears ready for analysis")
    
    st.markdown("### Business Insights")
    
    if business_insights:
        for insight in business_insights:
            st.success(f"📈 {insight}")
    else:
        st.info("No business insights generated")

def render_report_templates():
    """Render report template selection and generation"""
    
    st.markdown("## 📋 Report Templates")
    
    if st.session_state.current_dataframe is None:
        st.warning("⚠️ Please upload a dataset first")
        return
    
    # Template selection
    templates = {
        "executive_summary": "Executive Summary",
        "data_quality": "Data Quality Report",
        "statistical_analysis": "Statistical Analysis Report",
        "comprehensive": "Comprehensive Analysis Report"
    }
    
    selected_template = st.selectbox("Select report template:", 
                                   list(templates.keys()),
                                   format_func=lambda x: templates[x])
    
    if st.button("📄 Generate Report", key="generate_template_report"):
        with st.spinner("Generating report..."):
            
            if selected_template == "executive_summary":
                insights = st.session_state.ai_insights or generate_comprehensive_insights(st.session_state.current_dataframe)
                report = create_executive_summary(st.session_state.current_dataframe, insights)
                
                # Display executive summary
                st.markdown(f"# {report['title']}")
                st.markdown(f"**Generated:** {report['generated_at']}")
                
                st.markdown("## Key Findings")
                for finding in report['key_findings']:
                    st.write(f"• {finding}")
                
                st.markdown("## Data Characteristics")
                char = report['data_characteristics']
                st.write(f"• **Size:** {char['size']}")
                st.write(f"• **Complexity:** {char['complexity']}")
                st.write(f"• **Quality:** {char['quality']}")
                
                st.markdown("## Recommendations")
                for rec in report['recommendations']:
                    st.write(f"• {rec}")
                
                st.markdown("## Next Steps")
                for step in report['next_steps']:
                    st.write(f"• {step}")
            
            # Add report to generated reports
            report_info = {
                "template": templates[selected_template],
                "generated_at": datetime.now(),
                "content": report if selected_template == "executive_summary" else {"type": selected_template}
            }
            st.session_state.generated_reports.append(report_info)
            
            st.success("✅ Report generated successfully!")

def render_generated_reports():
    """Render list of generated reports"""
    
    st.markdown("## 📚 Generated Reports")
    
    if not st.session_state.generated_reports:
        st.info("📄 No reports generated yet. Use the report builder to create reports!")
        return
    
    # Reports list
    for i, report_info in enumerate(reversed(st.session_state.generated_reports)):
        with st.expander(f"📄 {report_info['template']} - {report_info['generated_at'].strftime('%Y-%m-%d %H:%M')}", expanded=False):
            
            st.markdown(f"**Template:** {report_info['template']}")
            st.markdown(f"**Generated:** {report_info['generated_at'].strftime('%Y-%m-%d %H:%M:%S')}")
            
            # Display report content (simplified)
            if 'content' in report_info and isinstance(report_info['content'], dict):
                if 'title' in report_info['content']:
                    st.markdown("### Report Preview")
                    st.json(report_info['content'])
            
            col1, col2 = st.columns(2)
            
            with col1:
                if st.button("📥 Export Report", key=f"export_report_{i}"):
                    # Export functionality
                    report_json = json.dumps(report_info['content'], indent=2, default=str)
                    st.download_button(
                        label="Download JSON",
                        data=report_json,
                        file_name=f"report_{report_info['generated_at'].strftime('%Y%m%d_%H%M%S')}.json",
                        mime="application/json",
                        key=f"download_report_{i}"
                    )
            
            with col2:
                if st.button("🗑️ Delete Report", key=f"delete_report_{i}"):
                    # Remove report
                    report_index = len(st.session_state.generated_reports) - 1 - i
                    st.session_state.generated_reports.pop(report_index)
                    st.success("Report deleted!")
                    st.rerun()

def main():
    """Main function for AI Reports & Insights page"""
    
    # Initialize session state
    initialize_session_state()
    
    # Page header
    st.markdown("# 📋 AI Reports & Insights with PandasAI + Gemini 2.5")
    st.markdown("Generate comprehensive reports and insights through natural language conversation!")
    
    # Check if data is loaded
    if st.session_state.current_dataframe is None:
        st.warning("⚠️ Please upload a dataset first using the Data Upload page")
        if st.button("🔙 Go to Data Upload"):
            st.switch_page("pages/01_Data_Upload_Chat.py")
        return
    
    # Main layout tabs
    chat_tab, insights_tab, templates_tab, reports_tab = st.tabs([
        "💬 Conversational Builder", "🤖 AI Insights", "📋 Templates", "📚 Generated Reports"
    ])
    
    with chat_tab:
        render_conversational_report_builder()
    
    with insights_tab:
        render_automated_insights_panel()
    
    with templates_tab:
        render_report_templates()
    
    with reports_tab:
        render_generated_reports()
    
    # Sidebar with dataset info and controls
    with st.sidebar:
        st.markdown("## 📊 Dataset Info")
        
        df = st.session_state.current_dataframe
        st.metric("Rows", f"{df.shape[0]:,}")
        st.metric("Columns", df.shape[1])
        
        # Insights status
        if st.session_state.ai_insights:
            st.success("✅ AI Insights Generated")
        else:
            st.info("⏳ AI Insights Pending")
        
        # Reports count
        st.metric("Generated Reports", len(st.session_state.generated_reports))
        
        # Controls
        st.markdown("## 🔧 Controls")
        
        if st.button("🔄 Regenerate Insights"):
            st.session_state.ai_insights = {}
            st.success("Insights will be regenerated on next view")
            st.rerun()
        
        if st.button("🗑️ Clear All Reports"):
            st.session_state.generated_reports = []
            st.success("All reports cleared!")
            st.rerun()

if __name__ == "__main__":
    main()
