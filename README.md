# AI-Driven Data Explorer with PandasAI Integration

A comprehensive Streamlit web application that combines the power of PandasAI's conversational AI with Google's Gemini 2.5 Flash for automated exploratory data analysis, feature engineering, and intelligent insights generation.

## 🌟 Features

### 🤖 **PandasAI Conversational Interface**
- Natural language data queries and analysis
- Real-time conversation with your datasets
- Intelligent code generation and execution
- Multi-turn conversations with context retention

### 📊 **Multi-Page Application Structure**
1. **Data Upload & Chat Interface** - File upload with immediate PandasAI chat
2. **Conversational EDA** - Dual-panel EDA with chat and automated insights
3. **Feature Engineering & Selection** - AI-driven feature creation with FeatureWiz
4. **Interactive Visualizations** - Natural language chart generation
5. **AI Reports & Insights** - Comprehensive report builder with Gemini 2.5
6. **System Monitoring** - Performance analytics and resource monitoring

### 🔧 **Advanced Capabilities**
- **Multi-agent AI System**: PandasAI + Gemini 2.5 Flash integration
- **Automated EDA**: Statistical analysis, pattern detection, quality assessment
- **Feature Engineering**: Interaction features, polynomial features, datetime extraction
- **Smart Visualizations**: Context-aware chart recommendations
- **Performance Monitoring**: Real-time system and AI operation analytics
- **Export Functionality**: Reports, conversations, and analysis results

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- Google Gemini API key
- Streamlit

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd ai-data-explorer
```

2. **Install dependencies**
```bash
pip install -r requirements.txt
```

3. **Set up environment variables** (optional)
```bash
# Create .env file
echo "GEMINI_API_KEY=your_api_key_here" > .env
```

4. **Run the application**
```bash
streamlit run streamlit_app/main.py
```

### First Steps
1. Open the application in your browser (typically `http://localhost:8501`)
2. Enter your Gemini API key in the sidebar
3. Upload a dataset or use sample data
4. Start conversing with your data using natural language!

## 📁 Project Structure

```
ai-data-explorer/
├── streamlit_app/
│   ├── main.py                 # Main application entry point
│   ├── config/
│   │   └── pandasai_config.py  # PandasAI configuration
│   ├── components/
│   │   ├── pandasai_chat.py    # Chat interface component
│   │   ├── conversation_manager.py  # Conversation state management
│   │   ├── ai_integration.py   # AI integration layer
│   │   └── code_display.py     # Code display utilities
│   ├── utils/
│   │   └── pandasai_client.py  # PandasAI client wrapper
│   ├── styles/
│   │   └── custom.css          # Custom styling
│   └── pages/
│       ├── 01_Data_Upload_Chat.py
│       ├── 02_Conversational_EDA.py
│       ├── 03_Feature_Engineering_AI.py
│       ├── 04_Interactive_Visualizations.py
│       ├── 05_AI_Reports_Insights.py
│       └── 06_System_Monitoring.py
├── requirements.txt
└── README.md
```

## 🔑 Key Components

### PandasAI Integration
- **Natural Language Processing**: Convert questions to pandas/python code
- **Context Awareness**: Maintains conversation history and dataset context
- **Code Generation**: Automatic code generation with explanation
- **Error Handling**: Graceful error handling with suggestions

### Gemini 2.5 Flash Integration
- **Enhanced Analysis**: Advanced pattern recognition and insights
- **Report Generation**: Comprehensive automated reporting
- **Feature Suggestions**: Intelligent feature engineering recommendations
- **Quality Assessment**: Automated data quality evaluation

### Multi-Agent Coordination
- **Collaborative Analysis**: PandasAI and Gemini working together
- **Cross-Validation**: Multiple AI perspectives on data insights
- **Enhanced Accuracy**: Improved analysis through AI collaboration

## 💡 Usage Examples

### Basic Data Exploration
```
"What are the main characteristics of this dataset?"
"Show me the correlation between numerical variables"
"Which columns have missing values?"
```

### Advanced Analysis
```
"Create interaction features between price and quantity"
"Generate a comprehensive data quality report"
"What patterns can you identify in the sales data?"
```

### Visualization Requests
```
"Create a scatter plot of sales vs profit colored by region"
"Show me the distribution of customer ages"
"Generate a dashboard with key metrics"
```

## 🛠️ Configuration

### PandasAI Settings
- Model configuration in `config/pandasai_config.py`
- Custom prompts and templates
- Response formatting options

### API Configuration
- Gemini API key management
- Rate limiting and error handling
- Model selection and parameters

### UI Customization
- Custom CSS in `styles/custom.css`
- Theme and color scheme
- Component styling

## 📊 Supported Data Formats

- **CSV** - Comma-separated values
- **Excel** - .xlsx, .xls files
- **JSON** - JavaScript Object Notation
- **Parquet** - Columnar storage format

## 🔍 Features in Detail

### Conversational EDA
- Automated statistical analysis
- Pattern detection and anomaly identification
- Data quality assessment
- Missing value analysis
- Correlation analysis with visualizations

### Feature Engineering
- **Interaction Features**: Mathematical combinations of variables
- **Polynomial Features**: Higher-order feature generation
- **Datetime Features**: Time-based feature extraction
- **Aggregation Features**: Group-based statistical features
- **Binning Features**: Categorical binning of continuous variables

### Intelligent Visualizations
- Context-aware chart recommendations
- Natural language chart generation
- Interactive plotly visualizations
- Chart gallery and export functionality

### Performance Monitoring
- Real-time system metrics (CPU, Memory, Disk)
- PandasAI query performance tracking
- Response time analytics
- Success/failure rate monitoring

## 🚀 Advanced Features

### Session Management
- Persistent conversation history
- Dataset state management
- Multi-conversation support
- Export/import functionality

### Error Handling
- Graceful error recovery
- Helpful error messages
- Suggestion system for corrections
- Fallback mechanisms

### Export Capabilities
- Conversation exports (JSON, CSV)
- Report generation (PDF, HTML)
- Code snippet extraction
- Analysis result downloads

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **PandasAI** - For the conversational AI framework
- **Google Gemini** - For advanced AI capabilities
- **Streamlit** - For the web application framework
- **Plotly** - For interactive visualizations
- **FeatureWiz** - For automated feature selection

## 📞 Support

For support, please open an issue on GitHub or contact the development team.

## 🔮 Future Enhancements

- [ ] Multi-dataset support
- [ ] Advanced ML model integration
- [ ] Real-time data streaming
- [ ] Collaborative features
- [ ] API endpoints for programmatic access
- [ ] Mobile-responsive design improvements

---

**Built with ❤️ using PandasAI, Gemini 2.5 Flash, and Streamlit**
