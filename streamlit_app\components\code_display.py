"""
Code Display Component with Syntax Highlighting
"""
import streamlit as st
from streamlit_ace import st_ace
import pandas as pd
from typing import Optional, Dict, Any

class CodeDisplayComponent:
    """Component for displaying and editing code with syntax highlighting"""
    
    def __init__(self):
        self.supported_languages = ["python", "sql", "json", "markdown", "yaml"]
    
    def render_code_viewer(
        self, 
        code: str, 
        language: str = "python", 
        theme: str = "monokai",
        height: int = 200,
        show_copy_button: bool = True,
        show_run_button: bool = False,
        key_suffix: str = ""
    ) -> Optional[str]:
        """
        Render code viewer with syntax highlighting
        
        Args:
            code: Code to display
            language: Programming language for syntax highlighting
            theme: Editor theme
            height: Height of the editor
            show_copy_button: Whether to show copy button
            show_run_button: Whether to show run button
            key_suffix: Suffix for unique keys
            
        Returns:
            Modified code if edited, None otherwise
        """
        
        col1, col2, col3 = st.columns([6, 1, 1])
        
        with col1:
            st.markdown(f"**Generated {language.title()} Code:**")
        
        with col2:
            if show_copy_button:
                if st.button("📋 Copy", key=f"copy_code_{key_suffix}"):
                    st.code(code, language=language)
                    st.success("Code copied to display!")
        
        with col3:
            if show_run_button:
                if st.button("▶️ Run", key=f"run_code_{key_suffix}"):
                    return self._execute_code(code, language)
        
        # Display code with syntax highlighting
        displayed_code = st_ace(
            value=code,
            language=language,
            theme=theme,
            height=height,
            auto_update=False,
            key=f"code_editor_{key_suffix}",
            annotations=None,
            wrap=True,
            font_size=14
        )
        
        return displayed_code if displayed_code != code else None
    
    def render_code_comparison(
        self, 
        original_code: str, 
        modified_code: str,
        language: str = "python",
        key_suffix: str = ""
    ):
        """Render side-by-side code comparison"""
        
        st.markdown("**Code Comparison:**")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("*Original:*")
            st_ace(
                value=original_code,
                language=language,
                theme="github",
                height=300,
                auto_update=False,
                key=f"original_code_{key_suffix}",
                readonly=True
            )
        
        with col2:
            st.markdown("*Modified:*")
            st_ace(
                value=modified_code,
                language=language,
                theme="monokai",
                height=300,
                auto_update=False,
                key=f"modified_code_{key_suffix}",
                readonly=True
            )
    
    def render_interactive_code_editor(
        self,
        initial_code: str = "",
        language: str = "python",
        height: int = 400,
        key_suffix: str = ""
    ) -> str:
        """Render interactive code editor"""
        
        st.markdown("**Interactive Code Editor:**")
        
        # Editor controls
        col1, col2, col3 = st.columns([2, 1, 1])
        
        with col1:
            selected_language = st.selectbox(
                "Language:",
                self.supported_languages,
                index=self.supported_languages.index(language) if language in self.supported_languages else 0,
                key=f"lang_select_{key_suffix}"
            )
        
        with col2:
            theme = st.selectbox(
                "Theme:",
                ["monokai", "github", "tomorrow", "twilight", "solarized_dark", "solarized_light"],
                key=f"theme_select_{key_suffix}"
            )
        
        with col3:
            font_size = st.slider("Font Size:", 10, 20, 14, key=f"font_size_{key_suffix}")
        
        # Code editor
        edited_code = st_ace(
            value=initial_code,
            language=selected_language,
            theme=theme,
            height=height,
            auto_update=True,
            key=f"interactive_editor_{key_suffix}",
            font_size=font_size,
            wrap=True,
            show_gutter=True,
            show_print_margin=True
        )
        
        return edited_code
    
    def _execute_code(self, code: str, language: str) -> Optional[str]:
        """Execute code safely (limited implementation)"""
        
        if language != "python":
            st.warning(f"Code execution not supported for {language}")
            return None
        
        try:
            # Very basic and limited code execution
            # In a real application, you'd want proper sandboxing
            if "import" in code or "exec" in code or "eval" in code:
                st.error("Code execution blocked for security reasons")
                return None
            
            # Only allow basic pandas operations
            allowed_operations = ["df.", "pd.", "print(", "len(", "type(", "str("]
            
            if not any(op in code for op in allowed_operations):
                st.error("Only basic pandas operations are allowed")
                return None
            
            st.info("Code execution is limited for security. Use PandasAI chat for full functionality.")
            return None
            
        except Exception as e:
            st.error(f"Code execution error: {str(e)}")
            return None
    
    def render_code_snippet_gallery(self, snippets: Dict[str, Dict[str, Any]], key_suffix: str = ""):
        """Render a gallery of code snippets"""
        
        st.markdown("**Code Snippet Gallery:**")
        
        if not snippets:
            st.info("No code snippets available")
            return
        
        # Create tabs for different categories
        categories = list(snippets.keys())
        tabs = st.tabs(categories)
        
        for i, (category, snippet_data) in enumerate(snippets.items()):
            with tabs[i]:
                for snippet_name, snippet_info in snippet_data.items():
                    with st.expander(f"📝 {snippet_name}"):
                        st.markdown(f"*{snippet_info.get('description', 'No description')}*")
                        
                        self.render_code_viewer(
                            code=snippet_info.get('code', ''),
                            language=snippet_info.get('language', 'python'),
                            height=150,
                            key_suffix=f"{key_suffix}_{category}_{snippet_name}"
                        )
    
    def render_code_diff(self, old_code: str, new_code: str, key_suffix: str = ""):
        """Render code diff visualization"""
        
        st.markdown("**Code Changes:**")
        
        # Simple diff implementation
        old_lines = old_code.split('\n')
        new_lines = new_code.split('\n')
        
        max_lines = max(len(old_lines), len(new_lines))
        
        for i in range(max_lines):
            old_line = old_lines[i] if i < len(old_lines) else ""
            new_line = new_lines[i] if i < len(new_lines) else ""
            
            if old_line != new_line:
                col1, col2 = st.columns(2)
                
                with col1:
                    if old_line:
                        st.markdown(f"❌ `{old_line}`")
                    else:
                        st.markdown("❌ *Line removed*")
                
                with col2:
                    if new_line:
                        st.markdown(f"✅ `{new_line}`")
                    else:
                        st.markdown("✅ *Line added*")
            else:
                if old_line:  # Only show unchanged lines if they exist
                    st.markdown(f"⚪ `{old_line}`")
