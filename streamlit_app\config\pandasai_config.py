"""
PandasAI Configuration Settings
"""
import os
from typing import Dict, Any

class PandasAIConfig:
    """Configuration class for PandasAI settings"""
    
    # Default configuration
    DEFAULT_CONFIG = {
        "model": "gemini-2.5-flash",
        "temperature": 0.1,
        "max_tokens": 2000,
        "conversation_memory": True,
        "enable_cache": True,
        "verbose": True,
        "enforce_privacy": True,
        "save_charts": True,
        "save_charts_path": "streamlit_app/assets/charts",
        "custom_whitelisted_dependencies": [
            "plotly",
            "seaborn", 
            "matplotlib",
            "numpy",
            "pandas",
            "scipy",
            "sklearn"
        ]
    }
    
    # Chat interface settings
    CHAT_CONFIG = {
        "max_history_length": 50,
        "enable_streaming": True,
        "show_code": True,
        "enable_follow_up": True,
        "auto_suggestions": True,
        "conversation_starters": [
            "What does this dataset contain?",
            "Show me basic statistics for all columns",
            "Are there any missing values?",
            "What are the data types of each column?",
            "Which columns have the most correlation?",
            "Show me the distribution of the target variable"
        ]
    }
    
    # Feature engineering prompts
    FEATURE_ENGINEERING_PROMPTS = {
        "interaction_features": "Create interaction features between {col1} and {col2}",
        "polynomial_features": "Generate polynomial features for numerical columns up to degree {degree}",
        "categorical_bins": "Create categorical bins for {column} with {n_bins} bins",
        "date_features": "Extract date components (year, month, day, weekday) from {column}",
        "ratio_features": "Create ratio features between {numerator} and {denominator}",
        "aggregation_features": "Create aggregation features grouped by {group_col}"
    }
    
    # Visualization templates
    VISUALIZATION_TEMPLATES = {
        "correlation_heatmap": "Create a correlation heatmap for numerical columns",
        "distribution_plots": "Show distribution plots for all numerical columns",
        "box_plots": "Create box plots for {column} grouped by {group_by}",
        "scatter_plot": "Create a scatter plot of {x} vs {y} colored by {color}",
        "bar_chart": "Create a bar chart showing {metric} by {category}",
        "time_series": "Create a time series plot for {column} over {time_col}"
    }
    
    @classmethod
    def get_config(cls) -> Dict[str, Any]:
        """Get the complete configuration"""
        return {
            "pandasai": cls.DEFAULT_CONFIG,
            "chat": cls.CHAT_CONFIG,
            "feature_engineering": cls.FEATURE_ENGINEERING_PROMPTS,
            "visualizations": cls.VISUALIZATION_TEMPLATES
        }
    
    @classmethod
    def get_api_key(cls) -> str:
        """Get API key from environment or return empty string"""
        return os.getenv("GEMINI_API_KEY", "")
    
    @classmethod
    def validate_config(cls, config: Dict[str, Any]) -> bool:
        """Validate configuration settings"""
        required_keys = ["model", "temperature", "max_tokens"]
        return all(key in config for key in required_keys)
