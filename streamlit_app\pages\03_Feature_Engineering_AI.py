"""
PandasAI Feature Engineering + FeatureWiz Selection Page
"""
import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from sklearn.preprocessing import StandardScaler, LabelEncoder, PolynomialFeatures
from sklearn.feature_selection import SelectKBest, f_regression, mutual_info_regression
import os
import sys
from datetime import datetime

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from components.pandasai_chat import PandasAIChatInterface
from components.ai_integration import AIIntegrationManager
from config.pandasai_config import PandasAIConfig

# Page configuration
st.set_page_config(
    page_title="Feature Engineering - AI Data Explorer",
    page_icon="⚙️",
    layout="wide"
)

def initialize_session_state():
    """Initialize session state for Feature Engineering page"""
    if 'api_key' not in st.session_state:
        st.session_state.api_key = ""
    
    if 'current_dataframe' not in st.session_state:
        st.session_state.current_dataframe = None
    
    if 'feature_chat_interface' not in st.session_state:
        st.session_state.feature_chat_interface = None
    
    if 'engineered_features' not in st.session_state:
        st.session_state.engineered_features = {}
    
    if 'feature_selection_results' not in st.session_state:
        st.session_state.feature_selection_results = {}
    
    if 'original_dataframe' not in st.session_state:
        st.session_state.original_dataframe = None

def create_interaction_features(dataframe, col1, col2, operation='multiply'):
    """Create interaction features between two columns"""
    
    try:
        if operation == 'multiply':
            new_feature = dataframe[col1] * dataframe[col2]
            feature_name = f"{col1}_x_{col2}"
        elif operation == 'add':
            new_feature = dataframe[col1] + dataframe[col2]
            feature_name = f"{col1}_plus_{col2}"
        elif operation == 'subtract':
            new_feature = dataframe[col1] - dataframe[col2]
            feature_name = f"{col1}_minus_{col2}"
        elif operation == 'divide':
            new_feature = dataframe[col1] / (dataframe[col2] + 1e-8)  # Add small value to avoid division by zero
            feature_name = f"{col1}_div_{col2}"
        elif operation == 'ratio':
            new_feature = dataframe[col1] / (dataframe[col1] + dataframe[col2] + 1e-8)
            feature_name = f"{col1}_ratio_{col2}"
        else:
            return None, None
        
        return new_feature, feature_name
        
    except Exception as e:
        st.error(f"Error creating interaction feature: {str(e)}")
        return None, None

def create_polynomial_features(dataframe, columns, degree=2):
    """Create polynomial features for specified columns"""
    
    try:
        poly = PolynomialFeatures(degree=degree, include_bias=False)
        poly_features = poly.fit_transform(dataframe[columns])
        
        # Get feature names
        feature_names = poly.get_feature_names_out(columns)
        
        # Create DataFrame with polynomial features
        poly_df = pd.DataFrame(poly_features, columns=feature_names, index=dataframe.index)
        
        # Remove original features (keep only new polynomial features)
        original_features = set(columns)
        new_features = {col: poly_df[col] for col in feature_names if col not in original_features}
        
        return new_features
        
    except Exception as e:
        st.error(f"Error creating polynomial features: {str(e)}")
        return {}

def create_binning_features(dataframe, column, n_bins=5, strategy='quantile'):
    """Create categorical bins from continuous variables"""
    
    try:
        if strategy == 'quantile':
            bins = pd.qcut(dataframe[column], q=n_bins, duplicates='drop')
        elif strategy == 'uniform':
            bins = pd.cut(dataframe[column], bins=n_bins)
        else:
            return None, None
        
        feature_name = f"{column}_binned_{n_bins}"
        
        # Create dummy variables for bins
        bin_dummies = pd.get_dummies(bins, prefix=feature_name)
        
        return bin_dummies, feature_name
        
    except Exception as e:
        st.error(f"Error creating binning features: {str(e)}")
        return None, None

def create_aggregation_features(dataframe, group_col, agg_col, agg_functions=['mean', 'std', 'count']):
    """Create aggregation features grouped by a categorical column"""
    
    try:
        agg_features = {}
        
        for func in agg_functions:
            if func in ['mean', 'std', 'min', 'max', 'median']:
                agg_result = dataframe.groupby(group_col)[agg_col].transform(func)
                feature_name = f"{agg_col}_{func}_by_{group_col}"
                agg_features[feature_name] = agg_result
            elif func == 'count':
                agg_result = dataframe.groupby(group_col)[agg_col].transform('count')
                feature_name = f"{group_col}_count"
                agg_features[feature_name] = agg_result
        
        return agg_features
        
    except Exception as e:
        st.error(f"Error creating aggregation features: {str(e)}")
        return {}

def extract_datetime_features(dataframe, datetime_col):
    """Extract features from datetime columns"""
    
    try:
        datetime_features = {}
        
        # Convert to datetime if not already
        if not pd.api.types.is_datetime64_any_dtype(dataframe[datetime_col]):
            datetime_series = pd.to_datetime(dataframe[datetime_col])
        else:
            datetime_series = dataframe[datetime_col]
        
        # Extract various datetime components
        datetime_features[f"{datetime_col}_year"] = datetime_series.dt.year
        datetime_features[f"{datetime_col}_month"] = datetime_series.dt.month
        datetime_features[f"{datetime_col}_day"] = datetime_series.dt.day
        datetime_features[f"{datetime_col}_dayofweek"] = datetime_series.dt.dayofweek
        datetime_features[f"{datetime_col}_quarter"] = datetime_series.dt.quarter
        datetime_features[f"{datetime_col}_is_weekend"] = (datetime_series.dt.dayofweek >= 5).astype(int)
        
        # Time-based features
        datetime_features[f"{datetime_col}_hour"] = datetime_series.dt.hour
        datetime_features[f"{datetime_col}_minute"] = datetime_series.dt.minute
        
        return datetime_features
        
    except Exception as e:
        st.error(f"Error extracting datetime features: {str(e)}")
        return {}

def perform_feature_selection(dataframe, target_column, method='mutual_info', k=10):
    """Perform feature selection using various methods"""
    
    try:
        # Prepare features and target
        feature_columns = [col for col in dataframe.columns if col != target_column]
        X = dataframe[feature_columns]
        y = dataframe[target_column]
        
        # Handle categorical variables by encoding them
        X_encoded = X.copy()
        categorical_columns = X.select_dtypes(include=['object', 'category']).columns
        
        for col in categorical_columns:
            le = LabelEncoder()
            X_encoded[col] = le.fit_transform(X[col].astype(str))
        
        # Fill missing values
        X_encoded = X_encoded.fillna(X_encoded.mean())
        
        # Perform feature selection
        if method == 'mutual_info':
            if y.dtype in ['object', 'category']:
                # Classification
                from sklearn.feature_selection import mutual_info_classif
                selector = SelectKBest(score_func=mutual_info_classif, k=min(k, len(feature_columns)))
            else:
                # Regression
                selector = SelectKBest(score_func=mutual_info_regression, k=min(k, len(feature_columns)))
        elif method == 'f_test':
            if y.dtype in ['object', 'category']:
                from sklearn.feature_selection import f_classif
                selector = SelectKBest(score_func=f_classif, k=min(k, len(feature_columns)))
            else:
                selector = SelectKBest(score_func=f_regression, k=min(k, len(feature_columns)))
        
        # Fit selector
        X_selected = selector.fit_transform(X_encoded, y)
        
        # Get selected feature names and scores
        selected_features = X_encoded.columns[selector.get_support()].tolist()
        feature_scores = selector.scores_
        
        # Create results dictionary
        results = {
            'selected_features': selected_features,
            'feature_scores': dict(zip(feature_columns, feature_scores)),
            'selection_method': method,
            'n_features_selected': len(selected_features)
        }
        
        return results
        
    except Exception as e:
        st.error(f"Error in feature selection: {str(e)}")
        return {}

def render_feature_engineering_interface():
    """Render the PandasAI feature engineering interface"""
    
    st.markdown("## ⚙️ PandasAI Feature Engineering")
    
    if st.session_state.current_dataframe is None:
        st.warning("⚠️ Please upload a dataset first")
        return
    
    # Initialize chat interface if not already done
    if st.session_state.feature_chat_interface is None and st.session_state.api_key:
        st.session_state.feature_chat_interface = PandasAIChatInterface("feature_chat")
        st.session_state.feature_chat_interface.initialize_client(
            st.session_state.api_key, 
            st.session_state.current_dataframe
        )
    
    if st.session_state.feature_chat_interface:
        # Feature engineering specific suggestions
        st.markdown("### 💡 Feature Engineering Suggestions")
        
        df = st.session_state.current_dataframe
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        categorical_cols = df.select_dtypes(include=['object', 'category']).columns.tolist()
        datetime_cols = df.select_dtypes(include=['datetime64']).columns.tolist()
        
        feature_suggestions = []
        
        if len(numeric_cols) >= 2:
            feature_suggestions.extend([
                f"Create interaction features between {numeric_cols[0]} and {numeric_cols[1]}",
                f"Generate polynomial features for {numeric_cols[0]}",
                f"Create ratio features between numerical columns"
            ])
        
        if len(categorical_cols) > 0 and len(numeric_cols) > 0:
            feature_suggestions.extend([
                f"Create aggregation features grouped by {categorical_cols[0]}",
                f"Generate statistical features for {numeric_cols[0]} by {categorical_cols[0]}"
            ])
        
        if len(datetime_cols) > 0:
            feature_suggestions.append(f"Extract date components from {datetime_cols[0]}")
        
        if len(numeric_cols) > 0:
            feature_suggestions.extend([
                f"Create categorical bins for {numeric_cols[0]}",
                "Generate log and square root transformations"
            ])
        
        # Display suggestions in columns
        if feature_suggestions:
            suggestion_cols = st.columns(2)
            for i, suggestion in enumerate(feature_suggestions[:6]):
                col = suggestion_cols[i % 2]
                with col:
                    if st.button(suggestion, key=f"feature_suggestion_{i}", use_container_width=True):
                        st.session_state.feature_chat_interface._process_user_input(suggestion)
        
        # Render chat interface
        st.session_state.feature_chat_interface.render_chat_interface(height=500, show_suggestions=False)
    else:
        st.warning("⚠️ Please configure your API key to enable AI feature engineering")

def render_manual_feature_engineering():
    """Render manual feature engineering tools"""
    
    st.markdown("## 🛠️ Manual Feature Engineering Tools")
    
    if st.session_state.current_dataframe is None:
        st.warning("⚠️ Please upload a dataset first")
        return
    
    df = st.session_state.current_dataframe
    
    # Feature engineering tabs
    interaction_tab, poly_tab, binning_tab, datetime_tab, agg_tab = st.tabs([
        "🔗 Interactions", "📈 Polynomial", "📊 Binning", "📅 DateTime", "📋 Aggregation"
    ])
    
    with interaction_tab:
        render_interaction_features_tool(df)
    
    with poly_tab:
        render_polynomial_features_tool(df)
    
    with binning_tab:
        render_binning_features_tool(df)
    
    with datetime_tab:
        render_datetime_features_tool(df)
    
    with agg_tab:
        render_aggregation_features_tool(df)

def render_interaction_features_tool(dataframe):
    """Render interaction features creation tool"""
    
    st.markdown("### Create Interaction Features")
    
    numeric_cols = dataframe.select_dtypes(include=[np.number]).columns.tolist()
    
    if len(numeric_cols) < 2:
        st.warning("Need at least 2 numerical columns for interaction features")
        return
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        col1_selected = st.selectbox("Select first column:", numeric_cols, key="interaction_col1")
    
    with col2:
        col2_selected = st.selectbox("Select second column:", 
                                   [col for col in numeric_cols if col != col1_selected], 
                                   key="interaction_col2")
    
    with col3:
        operation = st.selectbox("Operation:", 
                               ['multiply', 'add', 'subtract', 'divide', 'ratio'], 
                               key="interaction_op")
    
    if st.button("Create Interaction Feature", key="create_interaction"):
        new_feature, feature_name = create_interaction_features(dataframe, col1_selected, col2_selected, operation)
        
        if new_feature is not None:
            st.session_state.engineered_features[feature_name] = new_feature
            st.success(f"✅ Created feature: {feature_name}")
            
            # Show preview
            preview_df = pd.DataFrame({
                col1_selected: dataframe[col1_selected].head(),
                col2_selected: dataframe[col2_selected].head(),
                feature_name: new_feature.head()
            })
            st.dataframe(preview_df)

def render_polynomial_features_tool(dataframe):
    """Render polynomial features creation tool"""
    
    st.markdown("### Create Polynomial Features")
    
    numeric_cols = dataframe.select_dtypes(include=[np.number]).columns.tolist()
    
    if not numeric_cols:
        st.warning("No numerical columns available for polynomial features")
        return
    
    col1, col2 = st.columns(2)
    
    with col1:
        selected_cols = st.multiselect("Select columns:", numeric_cols, key="poly_cols")
    
    with col2:
        degree = st.slider("Polynomial degree:", 2, 4, 2, key="poly_degree")
    
    if selected_cols and st.button("Create Polynomial Features", key="create_poly"):
        poly_features = create_polynomial_features(dataframe, selected_cols, degree)
        
        if poly_features:
            for name, feature in poly_features.items():
                st.session_state.engineered_features[name] = feature
            
            st.success(f"✅ Created {len(poly_features)} polynomial features")
            
            # Show feature names
            st.write("**Created features:**")
            for name in poly_features.keys():
                st.write(f"- {name}")

def render_binning_features_tool(dataframe):
    """Render binning features creation tool"""
    
    st.markdown("### Create Binning Features")
    
    numeric_cols = dataframe.select_dtypes(include=[np.number]).columns.tolist()
    
    if not numeric_cols:
        st.warning("No numerical columns available for binning")
        return
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        selected_col = st.selectbox("Select column:", numeric_cols, key="binning_col")
    
    with col2:
        n_bins = st.slider("Number of bins:", 3, 10, 5, key="n_bins")
    
    with col3:
        strategy = st.selectbox("Binning strategy:", ['quantile', 'uniform'], key="binning_strategy")
    
    if st.button("Create Binning Features", key="create_binning"):
        bin_features, feature_name = create_binning_features(dataframe, selected_col, n_bins, strategy)
        
        if bin_features is not None:
            for col in bin_features.columns:
                st.session_state.engineered_features[col] = bin_features[col]
            
            st.success(f"✅ Created {len(bin_features.columns)} binning features")
            st.dataframe(bin_features.head())

def render_datetime_features_tool(dataframe):
    """Render datetime features extraction tool"""
    
    st.markdown("### Extract DateTime Features")
    
    # Check for datetime columns or columns that might be datetime
    datetime_cols = dataframe.select_dtypes(include=['datetime64']).columns.tolist()
    potential_datetime_cols = []
    
    for col in dataframe.columns:
        if 'date' in col.lower() or 'time' in col.lower():
            potential_datetime_cols.append(col)
    
    all_datetime_cols = list(set(datetime_cols + potential_datetime_cols))
    
    if not all_datetime_cols:
        st.warning("No datetime columns detected")
        return
    
    selected_col = st.selectbox("Select datetime column:", all_datetime_cols, key="datetime_col")
    
    if st.button("Extract DateTime Features", key="create_datetime"):
        datetime_features = extract_datetime_features(dataframe, selected_col)
        
        if datetime_features:
            for name, feature in datetime_features.items():
                st.session_state.engineered_features[name] = feature
            
            st.success(f"✅ Created {len(datetime_features)} datetime features")
            
            # Show preview
            preview_df = pd.DataFrame(datetime_features).head()
            st.dataframe(preview_df)

def render_aggregation_features_tool(dataframe):
    """Render aggregation features creation tool"""
    
    st.markdown("### Create Aggregation Features")
    
    categorical_cols = dataframe.select_dtypes(include=['object', 'category']).columns.tolist()
    numeric_cols = dataframe.select_dtypes(include=[np.number]).columns.tolist()
    
    if not categorical_cols or not numeric_cols:
        st.warning("Need both categorical and numerical columns for aggregation features")
        return
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        group_col = st.selectbox("Group by column:", categorical_cols, key="agg_group_col")
    
    with col2:
        agg_col = st.selectbox("Aggregate column:", numeric_cols, key="agg_col")
    
    with col3:
        agg_functions = st.multiselect("Aggregation functions:", 
                                     ['mean', 'std', 'min', 'max', 'median', 'count'],
                                     default=['mean', 'std'],
                                     key="agg_functions")
    
    if agg_functions and st.button("Create Aggregation Features", key="create_agg"):
        agg_features = create_aggregation_features(dataframe, group_col, agg_col, agg_functions)
        
        if agg_features:
            for name, feature in agg_features.items():
                st.session_state.engineered_features[name] = feature
            
            st.success(f"✅ Created {len(agg_features)} aggregation features")
            
            # Show preview
            preview_df = pd.DataFrame(agg_features).head()
            st.dataframe(preview_df)

def render_feature_selection_panel():
    """Render feature selection results panel"""
    
    st.markdown("## 🎯 Feature Selection Results")
    
    if st.session_state.current_dataframe is None:
        st.info("📊 Load a dataset to perform feature selection")
        return
    
    # Combine original dataframe with engineered features
    df = st.session_state.current_dataframe.copy()
    
    if st.session_state.engineered_features:
        for name, feature in st.session_state.engineered_features.items():
            df[name] = feature
    
    # Feature selection interface
    st.markdown("### Configure Feature Selection")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        target_column = st.selectbox("Select target column:", df.columns.tolist(), key="target_col")
    
    with col2:
        selection_method = st.selectbox("Selection method:", ['mutual_info', 'f_test'], key="selection_method")
    
    with col3:
        k_features = st.slider("Number of features to select:", 1, min(20, len(df.columns)-1), 10, key="k_features")
    
    if st.button("Perform Feature Selection", key="perform_selection"):
        with st.spinner("Performing feature selection..."):
            results = perform_feature_selection(df, target_column, selection_method, k_features)
            st.session_state.feature_selection_results = results
    
    # Display results
    if st.session_state.feature_selection_results:
        results = st.session_state.feature_selection_results
        
        st.markdown("### Feature Selection Results")
        
        # Selected features
        st.markdown("**Selected Features:**")
        selected_features = results.get('selected_features', [])
        
        for i, feature in enumerate(selected_features, 1):
            st.write(f"{i}. {feature}")
        
        # Feature scores visualization
        if 'feature_scores' in results:
            scores_df = pd.DataFrame([
                {"Feature": feature, "Score": score}
                for feature, score in results['feature_scores'].items()
            ]).sort_values('Score', ascending=False)
            
            fig = px.bar(
                scores_df.head(15),
                x='Score',
                y='Feature',
                orientation='h',
                title=f"Top 15 Feature Scores ({results.get('selection_method', 'Unknown')})"
            )
            st.plotly_chart(fig, use_container_width=True)

def main():
    """Main function for Feature Engineering page"""
    
    # Initialize session state
    initialize_session_state()
    
    # Page header
    st.markdown("# ⚙️ PandasAI Feature Engineering + FeatureWiz Selection")
    st.markdown("Create new features through conversation and systematic selection!")
    
    # Check if data is loaded
    if st.session_state.current_dataframe is None:
        st.warning("⚠️ Please upload a dataset first using the Data Upload page")
        if st.button("🔙 Go to Data Upload"):
            st.switch_page("pages/01_Data_Upload_Chat.py")
        return
    
    # Store original dataframe
    if st.session_state.original_dataframe is None:
        st.session_state.original_dataframe = st.session_state.current_dataframe.copy()
    
    # Main layout
    feature_col, selection_col = st.columns([3, 2])
    
    with feature_col:
        # Feature engineering interface
        render_feature_engineering_interface()
        
        # Manual tools
        with st.expander("🛠️ Manual Feature Engineering Tools", expanded=False):
            render_manual_feature_engineering()
    
    with selection_col:
        render_feature_selection_panel()
    
    # Engineered features summary
    if st.session_state.engineered_features:
        st.markdown("---")
        st.markdown("## 📋 Engineered Features Summary")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("Total Engineered Features", len(st.session_state.engineered_features))
        
        with col2:
            original_features = len(st.session_state.original_dataframe.columns)
            total_features = original_features + len(st.session_state.engineered_features)
            st.metric("Total Features", f"{original_features} → {total_features}")
        
        with col3:
            if st.button("🔄 Reset All Features"):
                st.session_state.engineered_features = {}
                st.session_state.feature_selection_results = {}
                st.success("All engineered features reset!")
                st.rerun()
        
        # Features list
        features_df = pd.DataFrame([
            {"Feature Name": name, "Type": "Engineered", "Data Type": str(feature.dtype)}
            for name, feature in st.session_state.engineered_features.items()
        ])
        
        st.dataframe(features_df, use_container_width=True)

if __name__ == "__main__":
    main()
